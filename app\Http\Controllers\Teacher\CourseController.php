<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Teacher\CourseRequest;
use App\Models\Course;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

class CourseController extends Controller
{
    public function index()
    {
        $user = auth()->guard('teacher')->user();

        $courses = $user->courses;

        return view('teacher.courses.index', compact('courses'));
    }

    public function create()
    {
        return view('teacher.courses.create');
    }

    public function store(CourseRequest $request)
    {
        $user = auth()->guard('teacher')->user();

        $data = $request->except('_token', 'sections');

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('courses');
        }

        $course = $user->courses()->create($data);

        if ($request->sections) {
            foreach ($request->sections as $section) {
                $course->sections()->create([
                    'name_ar' => $section['ar'],
                    'name_en' => $section['en'],
                ]);
            }
        }

        return redirect()->route('teacher.courses.index')->with('success', transWord('تمت الإضافة بنجاح'));
    }

    public function show(string $id)
    {
        $user = auth()->guard('teacher')->user();

        $course = $user->courses()->with(['teacher', 'subject', 'sections', 'lessons'])->findOrFail($id);

        return view('teacher.courses.show', compact('course'));
    }

    public function edit(string $id)
    {
        $user = auth()->guard('teacher')->user();

        $course = $user->courses()->findOrFail($id);

        return view('teacher.courses.edit', compact('course'));
    }

    public function update(CourseRequest $request, string $id)
    {
        $user = auth()->guard('teacher')->user();

        $course = $user->courses()->findOrFail($id);

        $data = $request->validated();

        if ($request->hasFile('image')) {
            if ($course->image) {
                Storage::delete($course->image);
            }

            $data['image'] = $request->file('image')->store('courses');
        } else {
            $data['image'] = $course->image;
        }

        $course->update($data);

        return redirect()->route('teacher.courses.index')->with('success', transWord('تم التحديث بنجاح'));
    }

    public function destroy(string $id)
    {
        //
    }

    public function activeCourse(Course $course): JsonResponse
    {
        $course->update([
            'status' => $course->status === 'active' ? 'disabled' : 'active',
        ]);

        if ($course->status === 'active') {
            return response()->json([
                'message' => transWord('تم تفعيل الكورس'),
                'verified' => true,
                'textValue' => transWord('مفعل'),
            ]);
        } else {
            return response()->json([
                'message' => transWord('تم إلغاء تفعيل الكورس'),
                'verified' => false,
                'textValue' => transWord('غير مفعل'),
            ]);
        }
    }
}
