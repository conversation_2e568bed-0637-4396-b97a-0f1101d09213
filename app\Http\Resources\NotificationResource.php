<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => app()->getLocale() == 'ar' ? $this->data['title_ar'] : $this->data['title_en'],
            'body' => app()->getLocale() == 'ar' ? $this->data['body_ar'] : $this->data['body_en'],
            'icon' => $this->data['icon'] ?? null,
        ];
    }
}
