<?php

namespace App\Console\Commands;

use App\Models\Student;
use App\Notifications\SubscriptionExpiredNotification;
use Carbon\Carbon;
use DB;
use Illuminate\Console\Command;

class RemoveExpiredSubscriptions extends Command
{
    protected $signature = 'subscriptions:remove-expired';

    protected $description = 'Remove expired subscriptions from student_books and student_notes, and notify students.';

    public function handle()
    {
        $this->info('Starting to remove expired subscriptions...');

        DB::beginTransaction();
        try {
            $today = Carbon::now();

            // Find students with expired book subscriptions
            $expiredBooks = DB::table('student_books')
                ->where('end_date', '<', $today)
                ->get();

            // Find students with expired note subscriptions
            $expiredNotes = DB::table('student_notes')
                ->where('end_date', '<', $today)
                ->get();

            // Get all affected student IDs
            $studentIds = collect($expiredBooks)->merge($expiredNotes)->pluck('student_id')->unique();

            // Delete expired subscriptions
            DB::table('student_books')->where('end_date', '<', $today)->delete();
            DB::table('student_notes')->where('end_date', '<', $today)->delete();

            // Notify affected students
            $students = Student::whereIn('id', $studentIds)->get();

            $notificationData = [
                'title_ar' => 'إنتهاء الاشتراك',
                'title_en' => 'Subscription Expired',
                'body_ar' => 'اشتراكك في الكتب أو الملاحظات قد انتهى. قم بتجديده للاستمرار في الوصول إلى المحتوى.',
                'body_en' => 'Your subscription to books or notes has expired. Renew to continue accessing the content.',
                'icon' => asset('app/images/notifications/register.png'),
            ];

            foreach ($students as $student) {
                $student->notify(new SubscriptionExpiredNotification($notificationData));
            }

            DB::commit();
            $this->info('Expired subscriptions removed successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            logger()->error('Error removing expired subscriptions: '.$e->getMessage());
            $this->error('An error occurred while removing expired subscriptions.');
        }
    }
}
