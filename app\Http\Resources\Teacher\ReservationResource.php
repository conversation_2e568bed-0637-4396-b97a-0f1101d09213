<?php

namespace App\Http\Resources\Teacher;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReservationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'student' => [
                'name' => $this->student->name,
                'image' => $this->student->image ? asset('storage/'.$this->student->image) : asset('app/images/user.png'),
                'classroom' => $this->student->classroom->name,
            ],
            'date' => $this->created_at?->format('Y-m-d') ?? now()->format('Y-m-d'),
            'status' => $this->status,
        ];
    }
}
