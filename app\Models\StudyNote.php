<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudyNote extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'unit_name_ar',
        'unit_name_en',
        'classroom_id',
        'subject_id',
        'image',
        'file',
        'price',
    ];

    public function getNameAttribute()
    {
        return $this->{'name_' . app()->getLocale()};
    }

    public function getUnitNameAttribute()
    {
        return $this->{'unit_name_' . app()->getLocale()};
    }

    public function classroom()
    {
        return $this->belongsTo(Classroom::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function items()
    {
        return $this->morphMany(Item::class, 'itemable');
    }

    public function exam()
    {
        return $this->hasOne(StudyNoteExam::class);
    }
}
