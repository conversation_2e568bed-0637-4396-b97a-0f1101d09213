<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Livestream extends Model
{
    use HasFactory;

    protected $fillable = [
        'teacher_id',
        'name',
        'url',
        'date',
        'time',
        'notes',
        'student_ids',
        'status',
    ];

    protected $casts = [
        'student_ids' => 'array',
        'date' => 'date',
        'time' => 'datetime',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }
}
