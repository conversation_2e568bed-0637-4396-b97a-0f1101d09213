<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Student\StudyNoteExamAttemptResource;

class StudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone_key' => $this->phone_key,
            'phone' => $this->phone,
            'image' => $this->image ? asset('storage/' . $this->image) : asset('app/images/user.png'),
            'isVerified' => (bool) $this->verified,
            'isNotified' => (bool) $this->is_notify,
            'isApple' => (bool) $this->isApple,
            'classroom' => ClassroomResource::make($this->classroom),
            'studyNoteExamAttempts' => StudyNoteExamAttemptResource::collection($this->studyNoteExamAttempts),
        ];
    }
}
