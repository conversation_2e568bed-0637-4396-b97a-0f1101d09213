<?php

namespace App\Http\Controllers\Api\Student;

use App\Models\StudyNote;
use App\Models\StudyNoteExam;
use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Student\StudyNoteResource;
use App\Http\Resources\Student\StudyNoteExamResource;
use App\Http\Resources\Student\StudentStudyNoteResource;
use App\Http\Requests\Api\Student\StudyNoteSubmitExamRequest;

class StudyNoteController extends Controller
{
    public function index()
    {
        $notes = StudyNote::where('classroom_id', auth()->user()->classroom_id)->paginate(10);

        return $this->response(new BaseCollection($notes, StudyNoteResource::class));
    }

    public function studentNotes()
    {
        $student = auth()->user();

        $notes = $student->notes()->paginate(10);

        return $this->response(new BaseCollection($notes, StudentStudyNoteResource::class));
    }

    public function exam($study_note)
    {
        $study_note = StudyNote::find($study_note);

        if (!$study_note?->exam) {
            return $this->response(null, transWord($study_note ? 'لا يوجد امتحان لهذه الملزمة' : 'الملزمة غير موجودة'), 404);
        }

        return $this->response(new StudyNoteExamResource($study_note->exam));
    }

    public function submitExam(StudyNoteSubmitExamRequest $request, string $id)
    {
        $exam = StudyNoteExam::find($id);

        if (! $exam) {
            return $this->response(null, transWord('الامتحان غير موجود'), 404);
        }

        $student = auth()->user();

        if ($student->studyNoteExamAttempts()->where('study_note_exam_id', $exam->id)->exists()) {
            return $this->response(null, transWord('لقد تم تسجيل الامتحان من قبل'), 400);
        }

        $score = 0;

        // Calculate score
        foreach ($request->answers as $response) {
            $question = $exam->questions()->findOrFail($response['question_id']);
            $answer = $question->answers()->findOrFail($response['answer_id']);

            // Increment score if the answer is correct
            if ($answer->is_correct) {
                $score++;
            }
        }

        $student->studyNoteExamAttempts()->create([
            'study_note_exam_id' => $exam->id,
            'score' => $score,
        ]);

        return $this->response([
            'total_questions' => $exam->questions->count(),
            'correct_answers' => $score,
            'final_score' => "{$score}/{$exam->questions->count()}",
        ], transWord('تم تسجيل الامتحان بنجاح'));
    }
}
