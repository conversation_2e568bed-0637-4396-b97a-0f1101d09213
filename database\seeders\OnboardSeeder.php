<?php

namespace Database\Seeders;

use App\Models\Onboard;
use Illuminate\Database\Seeder;

class OnboardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Onboard::insert([
            [
                'title_ar' => 'التعدد في الدروس',
                'title_en' => 'The number of lessons',
                'desc_ar' => 'هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحةهذا النص هو مثال لنص يمكن أن يستبدل هذا النص هو مثال لنص يمكن أن يستبدل',
                'desc_en' => 'This is a sample text that can be replaced in the same space This is a sample text that can be replaced This is a sample text that can be replaced',
                'image' => 'onboard/onboard-1.png',
            ],
            [
                'title_ar' => 'حصص تفاعلية',
                'title_en' => 'Interactive classes',
                'desc_ar' => 'هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحةهذا النص هو مثال لنص يمكن أن يستبدل هذا النص هو مثال لنص يمكن أن يستبدل',
                'desc_en' => 'This is a sample text that can be replaced in the same space This is a sample text that can be replaced This is a sample text that can be replaced',
                'image' => 'onboard/onboard-2.png',
            ],
            [
                'title_ar' => 'التعدد في الدروس',
                'title_en' => 'The number of lessons',
                'desc_ar' => 'هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحةهذا النص هو مثال لنص يمكن أن يستبدل هذا النص هو مثال لنص يمكن أن يستبدل',
                'desc_en' => 'This is a sample text that can be replaced in the same space This is a sample text that can be replaced This is a sample text that can be replaced',
                'image' => 'onboard/onboard-3.png',
            ],
        ]);
    }
}
