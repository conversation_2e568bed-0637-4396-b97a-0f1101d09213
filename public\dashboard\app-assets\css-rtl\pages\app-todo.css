.todo-application .content-area-wrapper {
  border : 1px solid #EBE9F1;
  border-radius : 0.428rem;
}

.todo-application .content-area-wrapper .sidebar .todo-sidebar {
  width : 260px;
  height : inherit;
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  background-color : #FFFFFF;
  border-top-right-radius : 0.428rem;
  border-bottom-right-radius : 0.428rem;
  -webkit-transition : all 0.3s ease, background 0s;
          transition : all 0.3s ease, background 0s;
}

.todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu {
  width : 100%;
  z-index : 3;
}

.todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu .add-task {
  padding : 1.5rem;
}

.todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu .sidebar-menu-list {
  position : relative;
  height : calc(100% - 80px);
}

.todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu .list-group .list-group-item {
  padding : 0.58rem 1.5rem;
  border : 0;
  font-weight : 500;
  letter-spacing : 0.4px;
  border-right : 2px solid transparent;
  border-radius : 0;
}

.todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu .list-group .list-group-item + .list-group-item.active {
  margin-top : 0;
}

.todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu .list-group .list-group-item:hover, .todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu .list-group .list-group-item:focus, .todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu .list-group .list-group-item.active {
  background : transparent;
  color : #7367F0;
}

.todo-application .content-area-wrapper .sidebar .todo-sidebar .todo-app-menu .list-group .list-group-item.active {
  border-color : #7367F0;
}

.todo-application .content-area-wrapper .ql-editor {
  padding-bottom : 0;
}

.todo-application .content-area-wrapper .content-right {
  width : calc(100% - 260px);
  border-right : 1px solid #EBE9F1;
}

.todo-application .content-area-wrapper .content-right .todo-app-list {
  height : inherit;
}

.todo-application .content-area-wrapper .content-right .app-fixed-search {
  padding : 0.35rem 0.5rem;
  border-bottom : 1px solid #EBE9F1;
  background-color : #FFFFFF;
  border-top-left-radius : 0.357rem;
}

.todo-application .content-area-wrapper .content-right .app-fixed-search .input-group:focus-within {
  box-shadow : none;
}

.todo-application .content-area-wrapper .content-right .app-fixed-search input, .todo-application .content-area-wrapper .content-right .app-fixed-search .input-group-text {
  border : 0;
  background-color : transparent;
}

.todo-application .content-area-wrapper .content-right .todo-title {
  margin-right : 0.5rem;
  margin-left : 0.5rem;
}

.todo-application .content-area-wrapper .content-right .completed .todo-title {
  color : #B9B9C3;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper {
  position : relative;
  height : calc(100% - 3.49rem);
  background-color : #FFFFFF;
  border-radius : 0;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list {
  padding : 0;
  margin : 0;
  list-style : none;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li {
  cursor : pointer;
  -webkit-transition : all 0.2s, border-color 0s;
          transition : all 0.2s, border-color 0s;
  position : relative;
  padding : 0.893rem 2rem;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li:not(:first-child) {
  border-top : 1px solid #EBE9F1;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li:hover {
  -webkit-transform : translateY(-4px);
      -ms-transform : translateY(-4px);
          transform : translateY(-4px);
  box-shadow : 0 3px 10px 0 #EBE9F1;
  -webkit-transition : all 0.2s;
          transition : all 0.2s;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li .todo-title-wrapper {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-pack : justify;
  -webkit-justify-content : space-between;
  -ms-flex-pack : justify;
          justify-content : space-between;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li .todo-title-area, .todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li .title-wrapper {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li .todo-item-action {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
  -webkit-box-pack : justify;
  -webkit-justify-content : space-between;
  -ms-flex-pack : justify;
          justify-content : space-between;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li .todo-item-action a {
  cursor : pointer;
  font-size : 1.2rem;
  line-height : 1.5;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li .badge-wrapper {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .todo-task-list li .badge-wrapper .badge:not(:last-child) {
  margin-left : 0.5rem;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .no-results {
  display : none;
  padding : 1.5rem;
  text-align : center;
}

.todo-application .content-area-wrapper .content-right .todo-task-list-wrapper .no-results.show {
  display : block;
}

.todo-application .todo-item-action .close {
  background : transparent !important;
  box-shadow : none !important;
  position : unset !important;
  -webkit-transform : none !important;
      -ms-transform : none !important;
          transform : none !important;
  -webkit-transition : none !important;
          transition : none !important;
}

.todo-application .todo-item .drag-icon {
  visibility : hidden;
  cursor : move;
  position : absolute;
  right : 0.2rem;
  width : 1.75rem;
  height : 4rem;
  padding : 0 5px;
}

.todo-application .todo-item:hover .drag-icon {
  visibility : visible;
}

.todo-application form .error:not(li):not(input) {
  color : #EA5455;
  font-size : 85%;
  margin-top : 0.25rem;
}

.gu-mirror {
  list-style-type : none;
  list-style : none;
  padding : 0.893rem 2rem;
  background-color : #FFFFFF;
  border-top : 1px solid #EBE9F1;
  border-bottom : 1px solid #EBE9F1;
  box-shadow : 0 0 10px 0 rgba(34, 41, 47, 0.25);
}

.gu-mirror .todo-title-wrapper {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-pack : justify;
  -webkit-justify-content : space-between;
  -ms-flex-pack : justify;
          justify-content : space-between;
}

.gu-mirror .todo-title-area, .gu-mirror .title-wrapper {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
}

.gu-mirror .todo-item-action {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
  -webkit-box-pack : justify;
  -webkit-justify-content : space-between;
  -ms-flex-pack : justify;
          justify-content : space-between;
}

.gu-mirror .todo-title {
  padding-right : 1rem;
}

.gu-mirror.completed .todo-title {
  color : #B9B9C3;
}

@-webkit-keyframes fadeIn {
  0% {
    opacity : 0;
    top : 100px;
  }
  75% {
    opacity : 0.5;
    top : 0;
  }
  100% {
    opacity : 1;
  }
}

@media (max-width: 991.98px) {
  .todo-application .content-area-wrapper .sidebar-left .todo-sidebar {
    -webkit-transform : translateX(110%);
        -ms-transform : translateX(110%);
            transform : translateX(110%);
    -webkit-transition : all 0.3s ease-in-out;
            transition : all 0.3s ease-in-out;
    right : 0;
    position : absolute;
    z-index : 5;
    border-top-right-radius : 0.25rem;
    border-bottom-right-radius : 0.25rem;
  }
  .todo-application .content-area-wrapper .sidebar-left.show .todo-sidebar {
    -webkit-transform : translateX(0%);
        -ms-transform : translateX(0%);
            transform : translateX(0%);
    -webkit-transition : all 0.3s ease;
            transition : all 0.3s ease;
  }
  .todo-application .content-area-wrapper .content-right {
    width : 100%;
    border-right : 0;
  }
  .todo-application .content-area-wrapper .content-right .app-fixed-search {
    border-top-right-radius : 0.357rem;
  }
  .todo-application .content-area-wrapper .todo-title-wrapper {
    -webkit-box-orient : vertical;
    -webkit-box-direction : normal;
    -webkit-flex-direction : column;
        -ms-flex-direction : column;
            flex-direction : column;
  }
  .todo-application .content-area-wrapper .todo-title-wrapper .title-wrapper {
    margin-bottom : 0.5rem;
  }
  .todo-application .content-area-wrapper .todo-title-wrapper .todo-title {
    display : -webkit-box;
    -webkit-line-clamp : 1;
    -webkit-box-orient : vertical;
    overflow : hidden;
  }
  .todo-application .content-area-wrapper .todo-title-wrapper .badge-wrapper {
    margin-left : auto !important;
  }
}

@media (max-width: 349.98px) {
  .todo-application .content-area-wrapper .sidebar .todo-sidebar {
    width : 230px;
  }
}