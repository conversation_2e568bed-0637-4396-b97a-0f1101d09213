<?php

namespace App\Jobs;

use App\Mail\SubscriptionMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendBulkEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected $emails, protected $data)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // foreach (array_chunk($this->emails, 50) as $emailChunk) {

        //     foreach ($emailChunk as $key => $email) {
        //         // dd($email);
        //         Mail::to($email)->send(new SubscriptionMail($this->data));

        //         // Log::info($key . ' - ' . $email);
        //     }
        //     sleep(2); // Adjust sleep time to respect rate limits
        // }
    }
}
