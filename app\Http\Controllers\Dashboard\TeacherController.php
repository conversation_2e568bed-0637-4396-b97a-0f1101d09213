<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Teacher;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class TeacherController extends Controller
{
    public function index(): View
    {
        $teachers = Teacher::all();

        return view('dashboard.teachers.index', compact('teachers'));
    }

    public function show(string $id)
    {
        $teacher = Teacher::with(['subjects', 'classrooms', 'subscription', 'livestreams', 'courses'])->findOrFail($id);

        return view('dashboard.teachers.show', compact('teacher'));
    }

    public function destroy(Teacher $teacher): JsonResponse
    {
        $teacher->delete();

        return response()->json([
            'message' => 'تم الحذف بنجاح',
        ]);
    }

    public function activeAccount(Teacher $teacher): JsonResponse
    {
        $teacher->update([
            'verified' => ! $teacher->verified,
        ]);

        if ($teacher->isAccountVerified) {
            return response()->json([
                'message' => transWord('تم تفعيل الحساب'),
                'verified' => true,
                'textValue' => transWord('مفعل'),
            ]);
        } else {
            return response()->json([
                'message' => transWord('تم إلغاء تفعيل الحساب'),
                'verified' => false,
                'textValue' => transWord('غير مفعل'),
            ]);
        }
    }
}
