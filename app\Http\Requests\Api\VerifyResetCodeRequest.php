<?php

namespace App\Http\Requests\Api;

class VerifyResetCodeRequest extends MasterApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'register_type' => 'required|in:student,teacher',
            'email' => 'required|email',
            'code' => 'required',
        ];
    }
}
