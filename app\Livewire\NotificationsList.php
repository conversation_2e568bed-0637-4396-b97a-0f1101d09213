<?php

namespace App\Livewire;

use Livewire\Component;

class NotificationsList extends Component
{
    public $notifications;

    public $count;

    public function mount($notifications)
    {
        $this->notifications = $notifications;
    }

    public function refreshNotifications()
    {
        $this->notifications = auth()->user()->notifications;
    }

    public function render()
    {
        return view('livewire.notifications-list', [
            'notifications' => $this->notifications,
        ]);
    }
}
