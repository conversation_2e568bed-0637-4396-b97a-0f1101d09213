<?php

namespace App\Http\Controllers\Api\Student;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Student\UpdateProfileImageRequest;
use App\Http\Requests\Api\Student\UpdateProfileRequest;
use App\Http\Requests\Api\UpdatePasswordRequest;
use App\Http\Resources\StudentResource;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    public function me()
    {
        $student = auth()->user();

        return $this->response(new StudentResource($student));
    }

    public function update(UpdateProfileRequest $request)
    {
        $student = auth()->user();

        $student->update($request->all());

        return $this->response(new StudentResource($student), transWord('تم التحديث بنجاح'));
    }

    public function updatePassword(UpdatePasswordRequest $request)
    {
        $student = auth()->user();

        if (! \Hash::check($request->old_password, $student->password)) {
            return $this->response(null, transWord('كلمة المرور القديمة غير صحيحة'), 422);
        }

        $student->update([
            'password' => $request->new_password,
        ]);

        return $this->response(new StudentResource($student), transWord('تم التحديث بنجاح'));
    }

    public function updateImage(UpdateProfileImageRequest $request)
    {
        $student = auth()->user();

        $data = $request->validated();

        if ($request->hasFile('image')) {
            if ($student->image) {
                Storage::delete($student->image);
            }

            $data['image'] = $request->file('image')->store('students', 'public');
        } else {
            $data['image'] = $student->image;
        }

        $student->update($data);

        return $this->response(new StudentResource($student), transWord('تم التحديث بنجاح'));
    }
}
