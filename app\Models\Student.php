<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Student extends Authenticatable
{
    use HasA<PERSON>Tokens, HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'phone_key',
        'phone',
        'password',
        'image',
        'verified',
        'is_notify',
        'classroom_id',
        'code',
    ];

    protected $casts = [
        'verified' => 'boolean',
        'password' => 'hashed',
    ];

    public function updateFirebaseToken($device_id, $firebase_token)
    {
        $this->firebaseTokens()->updateOrCreate(
            ['device_id' => $device_id],
            ['firebase_token' => $firebase_token]
        );
    }

    public function fullphone(): Attribute
    {
        return Attribute::make(
            get: fn($value) => "+{$this->phone_key}{$this->phone}"
        );
    }

    public function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => asset('storage/' . $this->image)
        );
    }

    public function isAccountVerified(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->verified != false
        );
    }

    public function getIsAppleAttribute()
    {
        return false;
    }

    public function firebaseTokens()
    {
        return $this->morphMany(FirebaseToken::class, 'tokenable');
    }

    public function classroom()
    {
        return $this->belongsTo(Classroom::class);
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'student_courses', 'student_id', 'course_id')->withPivot('is_completed');
    }

    public function cart()
    {
        return $this->hasOne(Cart::class);
    }

    public function books()
    {
        return $this->belongsToMany(Book::class, 'student_books', 'student_id', 'book_id');
    }

    public function notes()
    {
        return $this->belongsToMany(StudyNote::class, 'student_notes', 'student_id', 'study_note_id');
    }

    public function rates()
    {
        return $this->hasMany(TeacherRate::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    public function examAttempts()
    {
        return $this->hasMany(ExamAttempt::class);
    }

    public function studyNoteExamAttempts()
    {
        return $this->hasMany(StudyNoteExamAttempt::class);
    }

    public function finishedLessons()
    {
        return $this->hasMany(FinishedLesson::class);
    }

    public function reservations()
    {
        return $this->hasMany(TeacherReservation::class);
    }
}
