<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('study_note_questions', function (Blueprint $table) {
            $table->string('image')->nullable()->after('question_en');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('study_note_questions', function (Blueprint $table) {
            $table->dropColumn('image');
        });
    }
};
