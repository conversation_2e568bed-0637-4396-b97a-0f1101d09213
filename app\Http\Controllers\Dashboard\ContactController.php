<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Mail\SendReply;
use App\Models\Contact;
use App\Repositories\Contract\ContactRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    protected $contactRepository;

    public function __construct(ContactRepositoryInterface $contactRepository)
    {
        $this->contactRepository = $contactRepository;
    }

    public function index()
    {
        $contacts = $this->contactRepository->getWhereType('contact_us');

        return view('dashboard.contact.index', compact('contacts'));
    }

    public function complaintAndSuggestion()
    {
        $contacts = $this->contactRepository->getWhereType('complaint_and_suggestion');

        return view('dashboard.contact.complaints_and_suggestion', compact('contacts'));
    }

    public function show(Contact $contact)
    {
        return view('dashboard.contact.show', compact('contact'));
    }

    public function deleteMsg(Contact $contact)
    {
        $contact->delete();

        return response()->json([
            'message' => 'تم الحذف بنجاح',
        ]);
    }

    public function showReplyForm(Contact $contact)
    {
        return view('dashboard.contact.reply', \compact('contact'));
    }

    public function sendReply(Request $request)
    {
        $contact = $this->contactRepository->getWhere(['email' => $request->email])->first();

        if ($contact) {

            $data = $request->input('reply');

            Mail::to($contact->email)->send(new SendReply($data));

            $contact->update(['isReply' => true]);

            return redirect()->route('admin.contacts.show', $contact->id)->with('success', __('models.sent_reply'));
        }
    }
}
