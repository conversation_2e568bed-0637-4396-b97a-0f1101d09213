<?php

namespace App\Http\Controllers\Api\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Resources\Teacher\ReservationResource;

class HomeController extends Controller
{
    public function __invoke()
    {
        $reservations = auth()->user()->reservations()->limit(3)->get();
        $reservationsCount = auth()->user()->reservations()->count();
        $courses = auth()->user()->courses()->count();

        return $this->response([
            'reservations' => ReservationResource::collection($reservations),
            'reservations_count' => $reservationsCount,
            'courses' => $courses,
        ]);
    }
}
