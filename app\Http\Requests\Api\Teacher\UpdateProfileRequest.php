<?php

namespace App\Http\Requests\Api\Teacher;

use App\Http\Requests\Api\MasterApiRequest;

class UpdateProfileRequest extends MasterApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = auth()->user()->id;

        return [
            'name' => 'required|string|min:4|max:255',
            'email' => 'required|string|email:rfc,dns|max:255|unique:teachers,email,'.$id,
            'phone_key' => 'required|numeric',
            'phone' => 'required|digits_between:9,14|unique:teachers,phone,'.$id,
            'subject_id' => 'required|array',
            'subject_id.*' => 'required|exists:subjects,id',
            'classroom_id' => 'required|array',
            'classroom_id.*' => 'required|exists:classrooms,id',
        ];
    }
}
