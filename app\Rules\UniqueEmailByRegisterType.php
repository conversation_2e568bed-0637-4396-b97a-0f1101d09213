<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class UniqueEmailByRegisterType implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Get the register type from the request
        $registerType = request('register_type');

        // Check the appropriate table based on register type
        if ($registerType === 'student') {
            $exists = DB::table('students')->where('email', $value)->exists();
        } elseif ($registerType === 'teacher') {
            $exists = DB::table('teachers')->where('email', $value)->exists();
        } else {
            $exists = false;
        }

        // If the email exists, trigger the validation failure
        if ($exists) {
            $fail(transWord('البريد الالكتروني مستخدم من قبل'));
        }
    }
}
