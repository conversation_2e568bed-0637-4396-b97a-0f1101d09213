<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, HasRoles, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'lname',
        'email',
        'phone_key',
        'phone',
        'password',
        'code',
        'is_notify',
        'image',
        'verified_at',
        'verified',
        'code',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function fullphone(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => "+{$this->phone_key}{$this->phone}"
        );
    }

    public function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => asset('storage/'.$this->image)
        );
    }

    public function isAccountVerified(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => ! is_null($this->verified_at) && $this->verified != false
        );
    }

    public function permissionList(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                $badges = '';

                foreach ($this->permissions as $permission) {
                    $badges .= '<span class="badge badge-primary">'.htmlspecialchars(__("models.{$permission->name}"), ENT_QUOTES, 'UTF-8').'</span> ';
                }

                $html = <<<HTML
                $badges
                HTML;

                return $html;
            }
        );
    }

    public function updateFirebaseToken($firebase_token, $device_id)
    {
        if (! count($this->firebaseTokens()->where('device_id', $device_id)->get()) == 1) {
            $this->firebaseTokens()->create([
                'firebase_token' => $firebase_token,
                'device_id' => $device_id,
            ]);
        }
    }

    public function firebaseTokens(): HasMany
    {
        return $this->hasMany(FirebaseToken::class);
    }

    public function Locations(): HasMany
    {
        return $this->hasMany(Userlocation::class, 'user_id', 'id');
    }

    public function leaves(): HasMany
    {
        return $this->hasMany(Leave::class, 'delivery_id', 'id');
    }

    public function arrival(): HasMany
    {
        return $this->hasMany(Arrival::class, 'delivery_id', 'id');
    }

    public function storage(): HasMany
    {
        return $this->hasMany(Storage::class, 'delivery_id', 'id');
    }

    public function orders($isFilter = false, $minDate = null, $maxDate = null)
    {
        if (! $isFilter) {
            $leaves = $this->leaves;
            $arrival = $this->arrival;
            $storage = $this->storage;
        } else {

            $minDate = Carbon::parse($minDate)->toDateString();
            $maxDate = Carbon::parse($maxDate)->toDateString();

            // Apply date filters to the query builder instances
            $leaves = $this->leaves()->whereBetween('created_at', [$minDate, $maxDate])->get();
            $arrival = $this->arrival()->whereBetween('created_at', [$minDate, $maxDate])->get();
            $storage = $this->storage()->whereBetween('created_at', [$minDate, $maxDate])->get();
        }

        // Ensure all variables are collections and convert to base collection before merging
        $leavesBase = $leaves instanceof \Illuminate\Support\Collection ? $leaves->toBase() : collect($leaves);
        $arrivalBase = $arrival instanceof \Illuminate\Support\Collection ? $arrival->toBase() : collect($arrival);
        $storageBase = $storage instanceof \Illuminate\Support\Collection ? $storage->toBase() : collect($storage);

        // Merge the base collections
        $collection = $leavesBase->merge($arrivalBase)->merge($storageBase);

        // Filter non-delivered and delivered orders
        $non_delivered = $collection->filter(function ($value) {
            return $value->status->value != 'delivered';
        })->all();

        $delivered = $collection->filter(function ($value) {
            return $value->status->value == 'delivered';
        })->all();

        return [
            'non_delivered' => $non_delivered,
            'delivered' => $delivered,
            'orders_count' => $collection->count(),
        ];
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class, 'delivery_id', 'id');
    }
}
