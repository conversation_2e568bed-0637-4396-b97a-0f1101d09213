<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ClassroomResource;
use App\Models\Classroom;

class ClassroomController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke()
    {
        $classrooms = Classroom::all();

        return $this->response(ClassroomResource::collection($classrooms));
    }
}
