<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Subscription;

class SubscriptionController extends Controller
{
    public function index()
    {
        $subscriptions = Subscription::with(['teacher', 'plan'])->get();

        return view('dashboard.subscriptions.index', compact('subscriptions'));
    }

    public function activeSubscription(Subscription $subscription)
    {
        $subscription->update([
            'status' => $subscription->status === 'active' ? 'disabled' : 'active',
        ]);

        if ($subscription->status === 'active') {
            return response()->json([
                'message' => transWord('تم تفعيل الإشتراك'),
                'verified' => true,
                'textValue' => transWord('مفعل'),
            ]);
        } else {
            return response()->json([
                'message' => transWord('تم إلغاء تفعيل الإشتراك'),
                'verified' => false,
                'textValue' => transWord('غير مفعل'),
            ]);
        }
    }
}
