<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SettingTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('settings')->delete();

        Setting::insert([
            [
                'key' => 'logo',
                'neckname' => 'الشعار',
                'type' => 'file',
                'value' => 'setting/logo.png',
                'section' => 'images',
            ],
            [
                'key' => 'phone',
                'neckname' => 'الجوال',
                'type' => 'tel',
                'value' => '0137247681',
                'section' => 'contact',
            ],
            [
                'key' => 'whatsapp',
                'neckname' => 'واتساب',
                'type' => 'tel',
                'value' => '0137247681',
                'section' => 'contact',
            ],
            [
                'key' => 'email',
                'neckname' => 'البريد الإلكترونى',
                'type' => 'email',
                'value' => '<EMAIL>',
                'section' => 'contact',
            ],
        ]);
    }
}
