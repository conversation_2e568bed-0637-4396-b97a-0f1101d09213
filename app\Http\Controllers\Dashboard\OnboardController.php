<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\OnboardRequest;
use App\Models\Onboard;
use Illuminate\Support\Facades\Storage;

class OnboardController extends Controller
{
    public function index()
    {
        $onboards = Onboard::all();

        return view('dashboard.onboard.index', compact('onboards'));
    }

    public function create()
    {
        return view('dashboard.onboard.create');
    }

    public function store(OnboardRequest $request)
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('onboard', 'public');
        }

        Onboard::create($data);

        return redirect()->route('admin.onboards.index')->with('success', transWord('تمت الإضافة بنجاح'));
    }

    public function show(string $id)
    {
        //
    }

    public function edit(Onboard $onboard)
    {
        return view('dashboard.onboard.edit', compact('onboard'));
    }

    public function update(OnboardRequest $request, Onboard $onboard)
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            Storage::delete($onboard->image);

            $data['image'] = $request->file('image')->store('onboard', 'public');
        } else {
            $data['image'] = $onboard->image;
        }

        $onboard->update($data);

        return redirect()->route('admin.onboards.index')->with('success', transWord('تم التحديث بنجاح'));
    }

    public function destroy(Onboard $onboard)
    {
        if ($onboard->image) {
            Storage::delete($onboard->image);
        }

        $onboard->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }
}
