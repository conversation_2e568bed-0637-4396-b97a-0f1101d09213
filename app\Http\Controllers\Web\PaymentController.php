<?php

namespace App\Http\Controllers\Web;

use DB;
use App\Models\Payment;
use Illuminate\Http\Request;
use Applab\Sadad\Facades\Sadad;
use App\Http\Controllers\Controller;
use Applab\Sadad\Requests\WCORequest;

class PaymentController extends Controller
{
    public function initiatePayment(string $paymentId)
    {
        // جلب بيانات الدفع والعلاقة مع الطالب
        $payment = Payment::with('student')->findOrFail($paymentId);

        // منع تكرار الدفع
        if ($payment->status !== 'pending') {
            abort(404, 'Payment already processed or invalid.');
        }

        $webCheckoutReq = new WCORequest();
        $webCheckoutReq->total_amount = number_format($payment->amount, 2, '.', '');
        $webCheckoutReq->order_id = $payment->related_id;
        $webCheckoutReq->customer_mobile = $payment->student->phone ?? '0500000000';
        $webCheckoutReq->callback_url = route('payment.callback', $payment->id);

        // Determine product title based on payment type
        $productTitle = 'Payment';
        if ($payment->type === 'course') {
            $productTitle = 'Course Payment';
        } elseif ($payment->type === 'cart') {
            $productTitle = 'Cart Payment';
        }

        $webCheckoutReq->setProducts([
            [
                'id'       => $payment->related_id,
                'title'    => $productTitle,
                'quantity' => 1,
                'amount'   => number_format($payment->amount, 2, '.', ''),
                'type'     => 'line_item',
            ]
        ]);

        return Sadad::webCheckoutOne($webCheckoutReq);
    }

    public function handleCallback(Request $request, $paymentId)
    {
        // Log the SADAD response
        \Log::info('SADAD Callback', ['payment_id' => $paymentId, 'data' => $request->all()]);

        $payment = Payment::find($paymentId);

        if (!$payment) {
            $errors = collect(['Payment not found']);
            return view('sadad::web-checkout-error', ['errors' => $errors]);
        }

        // Check if payment is already processed
        if ($payment->status !== 'pending' && $payment->status !== 'failed') {
            return view('sadad::web-checkout-success', ['payment' => $payment]);
        }

        if ($payment->status === 'failed') {
            $errors = collect(['Payment failed or was declined.']);
            return view('sadad::web-checkout-error', ['errors' => $errors]);
        }

        // Process the SADAD response
        $transactionStatus = $request->input('transaction_status');
        $status = $request->input('STATUS');
        try {
            // Start transaction
            DB::beginTransaction();

            // If payment is successful (transaction_status = 3 or STATUS = TXN_SUCCESS)
            if ($transactionStatus == '3' || $status == 'TXN_SUCCESS') {
                // Skip transaction logging for now
                // Update payment status
                $payment->update([
                    'status' => 'paid'
                ]);

                // Process based on payment type
                if ($payment->type === 'course') {
                    // Add course to student
                    $student = $payment->student;
                    $student->courses()->attach($payment->related_id, [
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                } elseif ($payment->type === 'cart') {
                    // Process cart items
                    $student = $payment->student;
                    $cart = $student->cart;

                    if (!$cart) {
                        throw new \Exception('Cart not found');
                    }

                    // Process each cart item
                    foreach ($cart->items as $item) {
                        $itemType = $item->itemable_type;

                        if ($itemType === \App\Models\Book::class) {
                            // Add book to student
                            $student->books()->attach($item->itemable_id, [
                                'start_date' => now(),
                                'end_date' => now()->addMonth(),
                            ]);
                        } elseif ($itemType === \App\Models\StudyNote::class) {
                            // Add study note to student
                            $student->notes()->attach($item->itemable_id, [
                                'start_date' => now(),
                                'end_date' => now()->addMonth(),
                            ]);
                        }
                    }

                    // Clear the cart
                    $cart->items()->delete();
                    $cart->update([
                        'total' => 0,
                        'discount' => 0,
                        'total_after_discount' => 0
                    ]);
                }

                // Commit transaction
                DB::commit();

                return view('sadad::web-checkout-success', ['payment' => $payment]);
            } else {
                // Payment failed
                // Skip transaction logging for now
                $payment->update(['status' => 'failed']);

                // Commit transaction
                DB::commit();

                // Create a collection with error message
                $errors = collect(['Payment failed or was declined.']);

                return view('sadad::web-checkout-error', ['errors' => $errors]);
            }
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();

            \Log::error('Payment processing error: ' . $e->getMessage(), [
                'payment_id' => $payment->id,
                'exception' => $e
            ]);

            // Create a collection with error message
            $errors = collect(['An error occurred while processing your payment. Please contact support.']);

            return view('sadad::web-checkout-error', ['errors' => $errors]);
        }
    }
}
