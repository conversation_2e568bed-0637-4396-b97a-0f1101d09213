<?php

namespace App\Http\Controllers\Api\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Teacher\SubscriptionRequest;
use App\Http\Resources\Teacher\SubscriptionResource;
use App\Models\Plan;

class SubscriptionController extends Controller
{
    public function index()
    {
        $teacher = auth()->user();

        if (! $teacher->subscription) {
            return $this->response([], transWord('لا يوجد اشتراك'), 404);
        }

        return $this->response(new SubscriptionResource($teacher->subscription));
    }

    public function store(SubscriptionRequest $request)
    {
        $teacher = auth()->user();

        $plan = Plan::find($request->plan_id);

        if (! $plan) {
            return $this->response([], 'Plan not found', 404);
        }

        $startDate = now();

        $endDate = now()->addMonths($plan->duration_in_months);

        $total = $plan->price * $plan->duration_in_months;

        $teacher->subscription()->updateOrCreate(
            ['teacher_id' => $teacher->id],
            [
                'plan_id' => $plan->id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'plan_duration' => $plan->duration_in_months,
                'plan_price' => $plan->price,
                'total' => $total,
            ]
        );

        return $this->response([], transWord('تم الإشتراك بنجاح'));
    }

    public function update()
    {
        $teacher = auth()->user();

        if (! $teacher->subscription) {
            return $this->response([], transWord('لا يوجد اشتراك'), 404);
        }

        $teacher->subscription->update([
            'start_date' => now(),
            'end_date' => now()->addMonths($teacher->subscription->plan->duration_in_months),
            'plan_duration' => $teacher->subscription->plan->duration_in_months,
            'plan_price' => $teacher->subscription->plan->price,
            'total' => $teacher->subscription->plan->price * $teacher->subscription->plan->duration_in_months,
        ]);

        return $this->response([], transWord('تم تجديد الإشتراك بنجاح'));
    }
}
