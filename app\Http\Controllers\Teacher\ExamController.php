<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Teacher\ExamRequest;
use App\Models\Answer;
use App\Models\Exam;
use App\Models\Question;
use Illuminate\Support\Facades\DB;

class ExamController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->guard('teacher')->user();

        $exams = $user->exams;

        return view('teacher.exams.index', compact('exams'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = auth()->guard('teacher')->user();

        $courses = $user->courses;

        return view('teacher.exams.create', compact('courses'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ExamRequest $request)
    {
        $validated = $request->validated();

        // Get the authenticated teacher
        $user = auth()->guard('teacher')->user();

        // Check if the teacher already has an exam for the same course
        $existingExam = Exam::where('course_id', $validated['course_id'])
            ->where('teacher_id', $user->id)
            ->first();

        if ($existingExam) {
            return redirect()->back()
                ->withErrors(['course_id' => transWord('الكورس لديه اختبار بالفعل')])
                ->withInput();
        }

        try {
            // Start transaction
            DB::beginTransaction();

            // Create the exam
            $exam = Exam::create([
                'name_ar' => $validated['name_ar'],
                'name_en' => $validated['name_en'],
                'course_id' => $validated['course_id'],
                'teacher_id' => $user->id,
                'duration' => $validated['duration'],
            ]);

            // Loop through the questions and save them
            foreach ($validated['questions'] as $questionData) {
                // Create the question
                $question = Question::create([
                    'exam_id' => $exam->id,
                    'question_ar' => $questionData['question_ar'],
                    'question_en' => $questionData['question_en'],
                ]);

                // Create the answers for this question
                foreach ($questionData['answers'] as $index => $answerData) {
                    $isCorrect = ($index == $questionData['correct_answer']);
                    Answer::create([
                        'question_id' => $question->id,
                        'answer_ar' => $answerData['answer_ar'],
                        'answer_en' => $answerData['answer_en'],
                        'is_correct' => $isCorrect,
                    ]);
                }
            }

            // Commit transaction
            DB::commit();

            // Redirect after successful creation
            return redirect()->route('teacher.exams.index')->with('success', transWord('تمت الإضافة بنجاح'));
        } catch (\Exception $e) {
            // Rollback transaction on failure
            DB::rollBack();

            // Log the error (optional)
            Log::error('Error creating exam:', ['error' => $e->getMessage()]);

            // Redirect back with an error message
            return redirect()->back()
                ->withErrors(['error' => transWord('حدث خطأ أثناء إضافة الاختبار. الرجاء المحاولة لاحقًا')])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = auth()->guard('teacher')->user();

        $courses = $user->courses;

        $exam = $user->exams()->findOrFail($id);

        return view('teacher.exams.edit', compact('exam', 'courses'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ExamRequest $request, $id)
    {
        $validated = $request->validated();

        // Get the authenticated teacher
        $user = auth()->guard('teacher')->user();

        // Find the exam by ID
        $exam = $user->exams()->findOrFail($id);

        // Check if another exam exists for the same course by the same teacher
        $existingExam = Exam::where('course_id', $validated['course_id'])
            ->where('teacher_id', $user->id)
            ->where('id', '!=', $exam->id)
            ->first();

        if ($existingExam) {
            return redirect()->back()
                ->withErrors(['course_id' => transWord('الكورس لديه اختبار بالفعل')])
                ->withInput();
        }

        try {
            // Start transaction
            DB::beginTransaction();

            // Update the exam details
            $exam->update([
                'name_ar' => $validated['name_ar'],
                'name_en' => $validated['name_en'],
                'course_id' => $validated['course_id'],
                'duration' => $validated['duration'],
            ]);

            // Handle questions and answers
            foreach ($request->questions as $questionIndex => $questionData) {
                // Check if the question is new or needs to be updated
                if (isset($questionData['id'])) {
                    // Update existing question
                    $question = Question::findOrFail($questionData['id']);
                    $question->update([
                        'question_ar' => $questionData['question_ar'],
                        'question_en' => $questionData['question_en'],
                    ]);
                } else {
                    // Create a new question
                    $question = Question::create([
                        'exam_id' => $exam->id,
                        'question_ar' => $questionData['question_ar'],
                        'question_en' => $questionData['question_en'],
                    ]);
                }

                // Handle answers for this question
                foreach ($questionData['answers'] as $answerIndex => $answerData) {
                    // Skip empty answers (both 'answer_ar' and 'answer_en' are empty)
                    if (empty($answerData['answer_ar']) && empty($answerData['answer_en'])) {
                        continue;
                    }

                    // Check if the answer is new or needs to be updated
                    if (isset($answerData['id'])) {
                        // Update existing answer
                        $answer = Answer::findOrFail($answerData['id']);
                        $answer->update([
                            'answer_ar' => $answerData['answer_ar'],
                            'answer_en' => $answerData['answer_en'],
                            'is_correct' => ($answerIndex == $questionData['correct_answer']),
                        ]);
                    } else {
                        // Create a new answer
                        Answer::create([
                            'question_id' => $question->id,
                            'answer_ar' => $answerData['answer_ar'],
                            'answer_en' => $answerData['answer_en'],
                            'is_correct' => ($answerIndex == $questionData['correct_answer']),
                        ]);
                    }
                }
            }

            // Commit the transaction
            DB::commit();

            // Redirect with a success message
            return redirect()->route('teacher.exams.index')->with('success', transWord('تم التحديث بنجاح'));
        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollBack();

            // Log the error (optional)
            Log::error('Error updating exam:', ['error' => $e->getMessage()]);

            // Redirect back with an error message
            return redirect()->back()
                ->withErrors(['error' => transWord('حدث خطأ أثناء تحديث الاختبار. الرجاء المحاولة لاحقًا')])
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = auth()->guard('teacher')->user();

        $exam = $user->exams()->findOrFail($id);

        $exam->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }
}
