<?php

namespace App\Http\Requests\Dashboard;

use App\Rules\FileValidation;
use App\Rules\ImageValidation;
use App\Rules\TimeValidation;
use Illuminate\Foundation\Http\FormRequest;

class SettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        // dd($this->all());

        $key = $this->key;
        $type = $this->type;
        $section = $this->section;

        $rules = [];

        if ($type === 'file') {
            if ($section === 'images') {
                $rules = ['nullable', new ImageValidation, 'max:20480'];
            } elseif ($section === 'files') {
                $rules = ['nullable', new FileValidation, 'max:20480'];
            } elseif ($section === 'videos') {
                $rules = ['nullable', new ImageValidation('video'), 'max:20480'];
            }
        } elseif ($section === 'about') {
            if ($type === 'textarea') {
                $rules = ['required', 'string'];
            } else {
                $rules = ['required', 'string', 'max:255'];
            }
        } elseif ($section === 'contact') {
            if ($type === 'tel') {
                $rules = ['required', 'string', 'min:9', 'max:14'];
            } elseif ($type === 'email') {
                $rules = ['required', 'email:rfc,dns'];
            }
        } elseif ($section === 'numbers') {
            $rules = ['required', 'integer', 'gt:0'];
        } elseif ($section === 'links') {
            $rules = ['required', 'url'];
        } elseif ($section === 'time') {
            $rules = ['required', 'array', 'size:2', new TimeValidation];
        } elseif ($section === 'section') {
            $rules = ['required'];
        }

        return [
            $key => $rules,
        ];
    }
}
