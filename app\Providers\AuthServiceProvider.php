<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;

use App\Models\Teacher;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        Gate::before(function ($user, $ability) {
            if (! $user instanceof Teacher) {

                if ($user->hasRole('admin')) {
                    return true;
                }

                return $user->hasPermissionTo($ability);
            } else {
                return true;
            }
        });
    }
}
