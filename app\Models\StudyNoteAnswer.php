<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudyNoteAnswer extends Model
{
    use HasFactory;

    protected $fillable = [
        'study_note_question_id',
        'answer_ar',
        'answer_en',
    ];

    public function getAnswerAttribute()
    {
        return $this->{'answer_'.app()->getLocale()};
    }

    public function question()
    {
        return $this->belongsTo(StudyNoteQuestion::class);
    }
}
