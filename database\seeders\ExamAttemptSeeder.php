<?php

namespace Database\Seeders;

use App\Models\Exam;
use App\Models\ExamAttempt;
use App\Models\Student;
use Illuminate\Database\Seeder;

class ExamAttemptSeeder extends Seeder
{
    public function run(): void
    {
        $student = Student::first();

        $exam = Exam::first();

        $score = 0;

        // Calculate score based on random answers
        foreach ($exam->questions as $question) {
            $randomAnswer = $question->answers->random();

            // Increment score if the randomly selected answer is correct
            if ($randomAnswer->is_correct) {
                $score++;
            }
        }

        // Store the score in the exam_attempts table
        ExamAttempt::create([
            'exam_id' => $exam->id,
            'student_id' => $student->id,
            'score' => $score,
        ]);

        $this->command->info('Fake exam attempts created for 10 users.');

    }
}
