<?php

namespace App\Rules;

use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class TimeValidation implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // dd($value);

        $from = Carbon::parse($value['from']);

        $to = Carbon::parse($value['to']);

        if (! $from->lte($to)) {
            $fail(transWord('يجب عليك تحديد تاريخ الانتهاء بعد تاريخ البدء'));
        }
    }
}
