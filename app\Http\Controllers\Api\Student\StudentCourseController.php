<?php

namespace App\Http\Controllers\Api\Student;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Student\StudentCourseResource;
use Illuminate\Http\Request;

class StudentCourseController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'type' => 'required|in:current,compeleted',
        ]);

        $user = auth()->user();

        $type = $request->query('type');

        if ($type === 'compeleted') {
            $courses = $user->courses()->where('is_completed', true)->paginate(10);
        } else {
            $courses = $user->courses()->paginate(10);
        }

        return $this->response(new BaseCollection($courses, StudentCourseResource::class));
    }
}
