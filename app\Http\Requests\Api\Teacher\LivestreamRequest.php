<?php

namespace App\Http\Requests\Api\Teacher;

use App\Http\Requests\Api\MasterApiRequest;

class LivestreamRequest extends MasterApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|min:4|max:255',
            'url' => 'required|url',
            'date' => 'required|date',
            'time' => 'required|date_format:H:i',
            'notes' => 'nullable|string|min:4|max:255',
            'student_ids' => 'required|array',
        ];
    }
}
