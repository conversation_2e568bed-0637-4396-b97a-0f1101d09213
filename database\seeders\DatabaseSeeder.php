<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Role::create([
        //     'name' => 'admin',
        // ]);

        $this->call([
            // SettingTableSeeder::class,
            // UserSeeder::class,
            // ClassroomSeeder::class,
            // StudentSeeder::class,
            // SubjectSeeder::class,
            // TeacherSeeder::class,
            // OnboardSeeder::class,
            // CourseSeeder::class,
            // BannerSeeder::class,
            // BookSeeder::class,
            // CouponSeeder::class,
            // ExamSeeder::class,
            // PlanSeeder::class,
            StudyNoteSeeder::class,
        ]);
    }
}
