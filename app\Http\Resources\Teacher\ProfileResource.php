<?php

namespace App\Http\Resources\Teacher;

use App\Http\Resources\ClassroomResource;
use App\Http\Resources\Student\SubjectResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone_key' => $this->phone_key,
            'phone' => $this->phone,
            'image' => $this->image ? asset('storage/'.$this->image) : asset('app/images/user.png'),
            'subjects' => SubjectResource::collection($this->subjects),
            'classrooms' => ClassroomResource::collection($this->classrooms),
        ];
    }
}
