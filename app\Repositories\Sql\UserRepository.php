<?php

namespace App\Repositories\Sql;

use App\Models\User;
use App\Repositories\Contract\UserRepositoryInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class UserRepository extends BaseRepository implements UserRepositoryInterface
{
    public function __construct()
    {
        return $this->model = new User;
    }

    protected function generateVerficationCode($user)
    {
        $verificationCode = mt_rand(11111, 99999);

        $user->update(['code' => $verificationCode]);

        return true;
    }

    public function register(array $data)
    {
        $user = $this->model->create($data);

        $user->assignRole($data['role']);

        $user->updateFirebaseToken($data['firebase_token'], $data['device_id']);

        $this->generateVerficationCode($user);

        return $user;
    }

    public function verifyCode(array $data)
    {
        $user = auth()->user();

        if ($user->code != $data['code']) {
            return false;
        } else {
            if (! $user->verified) {
                $user->update([
                    'code' => null,
                    'verified_at' => now(),
                    'verified' => true,
                ]);
            } else {
                $user->update([
                    'code' => null,
                ]);
            }

            return true;
        }
    }

    public function login(array $data)
    {
        $user = $this->model->where(function ($query) use ($data) {
            $query->where('phone_key', $data['phone_key'])
                ->where('phone', $data['phone']);
        })->first();

        if (! empty($user) && Hash::check($data['password'], $user->password)) {
            if (Auth::attempt(['id' => $user->id, 'password' => $data['password']])) {
                $user->updateFirebaseToken($data['firebase_token'], $data['device_id']);

                return $user;
            }
        } else {
            return false;
        }
    }

    public function resetPassword(array $data)
    {
        $user = $this->model->where(function ($query) use ($data) {
            $query->where('phone_key', $data['phone_key'])
                ->where('phone', $data['phone']);
        })->first();

        if (! $user) {
            return false;
        }

        $this->generateVerficationCode($user);

        return $user;
    }

    public function changePassword(array $data)
    {
        $user = auth()->user();

        $user->update(['password' => $data['new_password']]);

        return true;
    }

    public function logout()
    {
        auth()->user()->currentAccessToken()->delete();

        return true;
    }

    public function updateProfile(array $data)
    {
        $user = auth()->user();

        if (isset($data['image']) && $data['image'] instanceof \Illuminate\Http\UploadedFile) {
            $data['image'] = $data['image']->store('students');
        } else {
            $data['image'] = $user->image;
        }

        $user->update($data);

        return $user;
    }

    public function updatePassword(array $data)
    {
        $user = auth()->user();

        $user->update(['password' => $data['new_password']]);

        return $user;
    }

    public function deleteAccount()
    {
        $user = auth()->user();
        $user->tokens()->delete();
        $user->delete();

        return true;
    }

    public function getUsersWhereRole(string|array $role)
    {
        if (is_string($role)) {
            $role = [$role];
        }

        return $this->model->with('roles')->whereHas('roles', function ($q) use ($role) {
            $q->whereIn('name', $role);
        })->get();
    }

    public function activeAccount($user)
    {
        if ($user->verified && $user->verified_at) {
            $user->update([
                'verified_at' => null,
                'verified' => false,
            ]);
        } else {
            $user->update([
                'verified_at' => now(),
                'verified' => true,
            ]);
        }
    }
}
