<?php

namespace App\Http\Controllers\Api\Student;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Student\TeacherRateRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Student\TeacherResource;
use App\Http\Resources\Teacher\FileResource;
use App\Models\Teacher;
use Illuminate\Http\Request;

class TeacherController extends Controller
{
    public function index(Request $request)
    {
        $filters = request()->only(['subject_id']);

        $teachers = Teacher::with(['subjects', 'rates'])->when(isset($filters['subject_id']), function ($query) use ($filters) {
            return $query->whereHas('subjects', function ($query) use ($filters) {
                $query->where('subject_id', $filters['subject_id']);
            });
        })->withCount('rates')
            ->orderBy('rates_count', 'desc')
            ->paginate(10);

        return $this->response(new BaseCollection($teachers, TeacherResource::class));
    }

    public function show(string $id)
    {
        $teacher = Teacher::with(['rates'])->find($id);

        if (! $teacher) {
            return $this->response([], transWord('المعلم غير موجود'), 404);
        }

        return $this->response(new TeacherResource($teacher));
    }

    public function rate(TeacherRateRequest $request, string $id)
    {
        $student = auth()->user();

        $teacher = Teacher::find($id);

        if (! $teacher) {
            return $this->response([], transWord('المعلم غير موجود'), 404);
        }

        $student->rates()->create([
            'teacher_id' => $teacher->id,
            'rate' => $request->rate,
            'comment' => $request->comment,
        ]);

        return $this->response([], transWord('تم التقييم بنجاح'));
    }

    public function reserve(string $id)
    {
        $student = auth()->user();

        $teacher = Teacher::find($id);

        if (! $teacher) {
            return $this->response([], transWord('المعلم غير موجود'), 404);
        }

        $teacher->reservations()->create([
            'student_id' => $student->id,
        ]);

        return $this->response([], transWord('تم الحجز بنجاح'));
    }

    public function files(string $id)
    {
        $student = auth()->user();

        $teacher = Teacher::find($id);

        if (! $teacher) {
            return $this->response([], transWord('المعلم غير موجود'), 404);
        }

        // check if student has reservation with this teacher with status accepted
        $reservation = $student->reservations()->where('teacher_id', $teacher->id)->where('status', 'accepted')->first();

        if (! $reservation) {
            return $this->response([], transWord('لا يمكنك تصفح الملفات لان ليس لديك حجز مع هذا المعلم'), 404);
        }

        $files = $teacher->files()->paginate(10);

        return $this->response(new BaseCollection($files, FileResource::class));
    }
}
