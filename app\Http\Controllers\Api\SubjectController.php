<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Student\SubjectResource;
use App\Models\Subject;
use Illuminate\Http\Request;

class SubjectController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke()
    {
        $subjects = Subject::all();

        return $this->response(SubjectResource::collection($subjects));
    }
}
