<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    public function home(): View
    {
        $currentYear = Carbon::now()->year;

        $data = $this->getChartData($currentYear);

        $months = $data['months'];
        $revenues = $data['revenues'];
        $totalRevenue = $data['total_revenue'];

        return view('dashboard.home', compact('months', 'revenues', 'currentYear', 'totalRevenue'));
    }

    public function updateRevenueChart(Request $request): JsonResponse
    {
        $year = $request->input('year');

        $data = $this->getChartData($year);

        return response()->json([
            'months' => $data['months'],
            'revenues' => $data['revenues'],
            'total_revenue' => $data['total_revenue'],
        ]);
    }

    protected function getChartData(int $year): array
    {
        // Get subscription data for the specified year, grouped by month
        $subscriptions = Subscription::selectRaw('SUM(total) as total_revenue, MONTH(created_at) as month, YEAR(created_at) as year')
            ->whereYear('created_at', $year)
            ->groupBy('year', 'month')
            ->orderBy('month', 'asc')
            ->get();

        // Create an array for all months (January to December)
        $allMonths = range(1, 12);

        // Initialize arrays to hold months and revenues
        $months = [];
        $revenues = [];

        // Initialize an associative array for subscription data indexed by month
        $subscriptionData = $subscriptions->keyBy('month')->mapWithKeys(function ($item) {
            return [$item->month => $item->total_revenue];
        });

        // Loop through all months and assign revenue data or zero if no data
        foreach ($allMonths as $month) {
            $months[] = Carbon::createFromFormat('m', $month)->format('F'); // Get month name
            $revenues[] = $subscriptionData->get($month, 0); // If no data, assign revenue as 0
        }

        $totalRevenue = array_sum($revenues);

        return [
            'months' => $months,
            'revenues' => $revenues,
            'total_revenue' => $totalRevenue,
        ];
    }
}
