<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\SettingRequest;
use App\Models\Setting;
use App\Repositories\Contract\SettingRepositoryInterface;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    protected $settingRepository;

    public function __construct(SettingRepositoryInterface $settingRepository)
    {

        $this->settingRepository = $settingRepository;
    } // end of contruct

    public function index()
    {
        $settings = $this->settingRepository->getWhere([['key', '!=', 'lat'], ['key', '!=', 'lng']], ['column' => 'id', 'dir' => 'ASC']);

        return view('dashboard.settings.index', compact('settings'));
    }

    public function edit(Setting $setting)
    {
        return view('dashboard.settings.edit', compact('setting'));
    }

    public function update(SettingRequest $request, Setting $setting)
    {
        $data = $request->validated();

        if ($request->lat && $request->lng) {
            $data['lat'] = $request->lat;
            $data['lng'] = $request->lng;
        }

        if ($request->hasFile($request->key)) {

            // Delete old internal_image
            Storage::delete($setting->value);

            // Upload new internal_image
            $data[$request->key] = $request->file($request->key)->store('setting');
        }

        $this->settingRepository->updateSetting($data);

        return redirect()->route('admin.settings.index')->with('success', __('models.update_success'));
    }
}
