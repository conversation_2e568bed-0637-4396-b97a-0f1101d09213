<?php

namespace App\Http\Requests\Api\Teacher;

use Illuminate\Foundation\Http\FormRequest;

class FileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|min:4|max:255',
            'type' => 'required|in:image,file',
            'file' => [
                'required',
                'max:5120', // Max file size in KB
                function ($attribute, $value, $fail) {
                    $type = $this->input('type'); // Get the 'type' input
                    if ($type === 'image' && ! in_array($value->getClientMimeType(), ['image/jpeg', 'image/png', 'image/jpg'])) {
                        $fail(transWord('الملف يجب ان يكون صورة بصيغة jpeg,png,jpg'));
                    }
                    if ($type === 'file' && ! in_array($value->getClientMimeType(), ['application/pdf', 'application/msword'])) {
                        $fail(transWord('الملف يجب ان يكون بصيغة pdf,doc'));
                    }
                },
            ],
        ];
    }
}
