<?php

namespace App\Http\Resources\Teacher;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'plan' => [
                'id' => $this->plan->id,
                'name' => $this->plan->name,
            ],
            'price' => $this->plan_price,
            'duration' => $this->plan_duration,
            'total' => $this->total,
            'start_date' => $this->start_date->translatedFormat('Y-M-d'),
            'end_date' => $this->end_date->translatedFormat('Y-M-d'),
            'remaining_days' => now()->diffInDays($this->end_date),

        ];
    }
}
