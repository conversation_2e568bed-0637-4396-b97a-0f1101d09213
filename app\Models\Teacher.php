<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Teacher extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'phone_key',
        'phone',
        'password',
        'image',
        'verified',
        'is_notify',
        'code',
    ];

    protected $casts = [
        'verified' => 'boolean',
        'is_notify' => 'boolean',
        'password' => 'hashed',
    ];

    public function fullphone(): Attribute
    {
        return Attribute::make(
            get: fn($value) => "{$this->phone_key}{$this->phone}"
        );
    }

    public function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => asset('storage/' . $this->image)
        );
    }

    public function isAccountVerified(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->verified != false
        );
    }

    public function getIsAppleAttribute()
    {
        return false;
    }

    public function updateFirebaseToken($device_id, $firebase_token): void
    {
        $this->firebaseTokens()->updateOrCreate(
            ['device_id' => $device_id],
            ['firebase_token' => $firebase_token]
        );
    }

    public function firebaseTokens(): MorphMany
    {
        return $this->morphMany(FirebaseToken::class, 'tokenable');
    }

    public function subjects(): BelongsToMany
    {
        return $this->belongsToMany(Subject::class, 'teacher_subjects', 'teacher_id', 'subject_id');
    }

    public function classrooms(): BelongsToMany
    {
        return $this->belongsToMany(Classroom::class, 'teacher_classrooms', 'teacher_id', 'classroom_id');
    }

    public function rates(): HasMany
    {
        return $this->hasMany(TeacherRate::class);
    }

    public function courses(): HasMany
    {
        return $this->hasMany(Course::class);
    }

    public function reservations(): HasMany
    {
        return $this->hasMany(TeacherReservation::class);
    }

    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class);
    }

    public function livestreams(): HasMany
    {
        return $this->hasMany(Livestream::class);
    }

    public function exams()
    {
        return $this->hasMany(Exam::class);
    }

    public function files()
    {
        return $this->hasMany(File::class);
    }
}
