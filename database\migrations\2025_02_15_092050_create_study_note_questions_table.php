<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('study_note_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('study_note_exam_id')->constrained('study_note_exams')->onDelete('cascade');
            $table->text('question_ar');
            $table->text('question_en');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('study_note_questions');
    }
};
