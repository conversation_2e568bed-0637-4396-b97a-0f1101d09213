<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudyNoteQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'study_note_exam_id',
        'question_ar',
        'question_en',
        'image',
    ];

    public function getQuestionAttribute()
    {
        return $this->{'question_' . app()->getLocale()};
    }

    public function answers()
    {
        return $this->hasMany(StudyNoteAnswer::class);
    }
}
