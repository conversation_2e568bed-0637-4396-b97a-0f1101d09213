<?php

namespace App\Repositories\Contract;

interface UserRepositoryInterface
{
    public function register(array $data);

    public function verifyCode(array $data);

    public function login(array $data);

    public function resetPassword(array $data);

    public function changePassword(array $data);

    public function logout();

    public function updateProfile(array $data);

    public function updatePassword(array $data);

    public function deleteAccount();

    public function getUsersWhereRole(string|array $role);

    public function activeAccount($user);
}
