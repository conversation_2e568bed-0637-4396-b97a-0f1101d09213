<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudyNoteExam extends Model
{
    use HasFactory;

    protected $fillable = [
        'study_note_id',
        'name_ar',
        'name_en',
        'duration',
    ];

    public function getNameAttribute()
    {
        return $this->{'name_' . app()->getLocale()};
    }

    public function questions()
    {
        return $this->hasMany(StudyNoteQuestion::class);
    }

    public function answers()
    {
        return $this->hasManyThrough(
            StudyNoteAnswer::class,
            StudyNoteQuestion::class
        );
    }

    public function studyNote()
    {
        return $this->belongsTo(StudyNote::class);
    }
}
