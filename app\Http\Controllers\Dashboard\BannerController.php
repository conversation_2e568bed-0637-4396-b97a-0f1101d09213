<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\BannerRequest;
use App\Models\Banner;
use Illuminate\Support\Facades\Storage;

class BannerController extends Controller
{
    public function index()
    {
        $banners = Banner::all();

        return view('dashboard.banners.index', compact('banners'));
    }

    public function create()
    {
        return view('dashboard.banners.create');
    }

    public function store(BannerRequest $request)
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('banners', 'public');
        }

        Banner::create($data);

        return redirect()->route('admin.banners.index')->with('success', transWord('تمت الإضافة بنجاح'));
    }

    public function show(string $id)
    {
        //
    }

    public function edit(Banner $banner)
    {
        return view('dashboard.banners.edit', compact('banner'));
    }

    public function update(BannerRequest $request, Banner $banner)
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            if ($banner->image) {
                Storage::delete($banner->image);
            }

            $data['image'] = $request->file('image')->store('banners', 'public');
        } else {
            $data['image'] = $banner->image;
        }

        $banner->update($data);

        return redirect()->route('admin.banners.index')->with('success', transWord('تم التحديث بنجاح'));
    }

    public function destroy(Banner $banner)
    {
        if ($banner->image) {
            Storage::delete($banner->image);
        }

        $banner->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }
}
