<?php

use App\Models\User;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Role;

/*curr
|--------------------------------------------------------------------------
| Detect Active Routes Function
|--------------------------------------------------------------------------
|
| Compare given routes with current route and return output if they match.
| Very useful for navigation, marking if the link is active.
|
*/

function isActiveRoute($route, $output = 'active')
{
    if (\Route::currentRouteName() == $route) {
        return $output;
    }
}

function hasActiveSub($parent_name, $output = 'sidebar-group-active open')
{
    if (strpos(request()->url(), $parent_name) !== false) {
        return $output;
    }

}

function isActiveSub($sub_name, $parent_name, $output = 'active')
{
    if (strpos(request()->url(), $parent_name) !== false && strpos(request()->url(), $sub_name)) {
        return $output;
    }
}

function areActiveRoutes(array $routes, $output = 'active show-sub')
{

    foreach ($routes as $route) {
        if (\Route::currentRouteName() == $route) {
            return $output;
        }
    }
}

function areActiveMainRoutes(array $routes, $output = 'active')
{

    foreach ($routes as $route) {
        if (\Route::currentRouteName() == $route) {
            return $output;
        }
    }
}

function getSetting($key, $lang = null)
{

    $sittingrepository = App::make('App\Repositories\Contract\SettingRepositoryInterface');

    if ($lang == null) {

        $setting = $sittingrepository->getWhere([['key', $key]])->first()['value'];
    } else {

        $setting = $sittingrepository->getWhere([['key', $key.'_'.$lang]])->first()['value'];
    }

    return $setting;
}

function transWord($word, $locale = null)
{

    if (! $locale) {
        $locale = app()->getLocale();
    }

    $translationsFile = 'translations.json';

    // Check if the translations file exists, and create it if not
    if (! file_exists($translationsFile)) {
        file_put_contents($translationsFile, json_encode([], JSON_PRETTY_PRINT));
    }

    // Load existing translations from the JSON file
    $translations = json_decode(file_get_contents($translationsFile), true);

    // Check if the translation already exists for the given word and locale
    if (isset($translations[$locale][$word])) {
        $translatedWord = $translations[$locale][$word];
    } else {
        // If not found, translate the word
        $translateClient = new \Stichoza\GoogleTranslate\GoogleTranslate;
        $translatedWord = $translateClient->setSource(null)->setTarget($locale)->translate($word);

        // Save the translated word to the JSON file
        $translations[$locale][$word] = $translatedWord;
        file_put_contents($translationsFile, json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    return $translatedWord;
}

function getCount(string $model, $type = null)
{
    $modelClass = "App\Models\\".ucfirst($model);

    $count = 0;

    if (class_exists($modelClass)) {
        $instance = new $modelClass;

        if ($type) {
            $count = $instance->where('type', $type)->count();
        } else {
            $count = $instance->count();
        }
    }

    return $count;
}

function getUserCount(string $role)
{
    // check role exists
    if (! Role::whereName($role)->first()) {
        $count = 0;
    } else {
        $count = User::role($role)->count();
    }

    return $count;
}

function getAdmin()
{

    return User::role('admin')->first();
}
