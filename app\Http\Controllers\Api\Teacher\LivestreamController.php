<?php

namespace App\Http\Controllers\Api\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Teacher\LivestreamRequest;

class LivestreamController extends Controller
{
    public function store(LivestreamRequest $request)
    {
        $teacher = auth()->user();

        $livestream = $teacher->livestreams()->create($request->validated());

        return $this->response([
            'url' => $livestream->url,
        ]);
    }

    public function endStream(string $id)
    {
        $livestream = auth()->user()->livestreams()->findOrFail($id);

        if (! $livestream) {
            return $this->response([], 'Livestream not found', 404);
        }

        $livestream->update([
            'status' => 'ended',
        ]);

        return $this->response([], 'Livestream ended');
    }
}
