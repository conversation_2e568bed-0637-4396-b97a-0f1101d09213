<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Teacher\LessonRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Owenoj\LaravelGetId3\GetId3;

class LessonController extends Controller
{
    public function index(string $courseId, string $sectionId)
    {
        $user = auth()->guard('teacher')->user();

        $section = $user->courses()->findOrFail($courseId)->sections()->findOrFail($sectionId);

        $lessons = $section->lessons;

        return view('teacher.lessons.index', compact('lessons'));
    }

    public function create(string $courseId, string $sectionId)
    {
        $user = auth()->guard('teacher')->user();

        $section = $user->courses()->findOrFail($courseId)->sections()->findOrFail($sectionId);

        return view('teacher.lessons.create', compact('section'));
    }

    public function store(LessonRequest $request, string $courseId, string $sectionId)
    {
        $user = auth()->guard('teacher')->user();

        $data = $request->validated();

        $data['course_id'] = $courseId;

        $section = $user->courses()->findOrFail($courseId)->sections()->findOrFail($sectionId);

        $section->lessons()->create($data);

        return redirect()->route('teacher.lessons.index', [$courseId, $sectionId])->with('success', transWord('تمت الإضافة بنجاح'));
    }

    public function edit(string $courseId, string $sectionId, string $lessonId)
    {
        $user = auth()->guard('teacher')->user();

        $lesson = $user->courses()->findOrFail($courseId)->sections()->findOrFail($sectionId)->lessons()->findOrFail($lessonId);

        return view('teacher.lessons.edit', compact('lesson'));
    }

    public function update(LessonRequest $request, string $courseId, string $sectionId, string $lessonId)
    {
        $user = auth()->guard('teacher')->user();

        $lesson = $user->courses()->findOrFail($courseId)->sections()->findOrFail($sectionId)->lessons()->findOrFail($lessonId);

        $data = $request->validated();

        $data['is_free'] = $request->is_free ? 1 : 0;

        $lesson->update($data);

        return redirect()->route('teacher.lessons.index', [$courseId, $sectionId])->with('success', transWord('تم التحديث بنجاح'));
    }

    public function destroy(string $courseId, string $sectionId, string $lessonId)
    {
        $user = auth()->guard('teacher')->user();

        $lesson = $user->courses()->findOrFail($courseId)->sections()->findOrFail($sectionId)->lessons()->findOrFail($lessonId);

        if ($lesson->video) {
            Storage::delete($lesson->video);
        }

        $lesson->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }

    public function upload(Request $request, string $courseId, string $sectionId, ?string $lessonId = null)
    {
        $user = auth()->guard('teacher')->user();

        $checkCourseSection = $user->courses()->findOrFail($courseId)->sections()->findOrFail($sectionId);

        if (! $checkCourseSection) {
            return response()->json(['message' => 'Section or Course not found'], 404);
        }

        // Path for saving the video
        $path = "courses/{$courseId}/sections/{$sectionId}/videos";
        $fileName = now()->timestamp.'_'.str_replace(' ', '_', $request->file('file')->getClientOriginalName());

        // Check if a lesson ID is provided for updating
        if ($lessonId) {
            $lesson = $checkCourseSection->lessons()->findOrFail($lessonId);

            if ($lesson->video) {
                // Delete the old video file
                Storage::disk('public')->delete($lesson->video);
            }

            // Save the new video file
            $filePath = $request->file('file')->storeAs($path, $fileName, 'public');

            $track = new GetId3(request()->file('file'));

            $durationInMinutes = $track->getPlaytimeSeconds() / 60;

            // Update the lesson with the new video path and duration
            $lesson->update([
                'video' => $filePath,
                'duration' => (int) $durationInMinutes,
            ]);

            return response()->json([
                'message' => 'Lesson video updated successfully',
                'path' => $filePath,
                'duration' => (int) $durationInMinutes,
            ], 200);
        }

        // If no lesson ID, save the new video without associating with a lesson
        $filePath = $request->file('file')->storeAs($path, $fileName, 'public');

        $track = new GetId3(request()->file('file'));

        $durationInMinutes = $track->getPlaytimeSeconds() / 60;

        // Return the file path and video duration
        return response()->json([
            'path' => $filePath,
            'duration' => (int) $durationInMinutes,
        ], 200);
    }
}
