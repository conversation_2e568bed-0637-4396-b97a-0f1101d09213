.knowledge-base-bg {
  background-size : cover;
  background-color : rgba(115, 103, 240, 0.12) !important;
}

.knowledge-base-bg .kb-search-input .input-group:focus-within {
  box-shadow : none;
}

.kb-search-content-info .kb-search-content .card-img-top {
  background-color : #FCFCFC;
}

.kb-search-content-info .no-result.no-items {
  display : none;
}

.kb-title {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
}

@media (min-width: 768px) {
  .knowledge-base-bg .kb-search-input .input-group {
    width : 576px;
    margin : 0 auto;
  }
}

@media (min-width: 992px) {
  .knowledge-base-bg .card-body {
    padding : 8rem;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .knowledge-base-bg .card-body {
    padding : 6rem;
  }
}