<?php

namespace App\Http\Controllers\Api\Student;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Student\BookResource;
use App\Http\Resources\Student\StudentBookResource;
use App\Models\Book;

class BookController extends Controller
{
    public function index()
    {
        $books = Book::where('classroom_id', auth()->user()->classroom_id)->paginate(10);

        return $this->response(new BaseCollection($books, BookResource::class));
    }

    public function studentBooks()
    {
        $student = auth()->user();

        $books = $student->books()->paginate(10);

        return $this->response(new BaseCollection($books, StudentBookResource::class));
    }
}
