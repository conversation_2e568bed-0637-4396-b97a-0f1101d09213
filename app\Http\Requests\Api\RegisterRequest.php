<?php

namespace App\Http\Requests\Api;

use App\Rules\UniqueEmailByRegisterType;
use App\Rules\UniquePhoneByRegisterType;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends MasterApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'register_type' => 'required|in:student,teacher',
            'name' => 'required|string|min:4|max:255',
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                new UniqueEmailByRegisterType(request()->register_type),
            ],
            'phone' => [
                'nullable',
                'digits_between:9,14',
                new UniquePhoneByRegisterType(request()->register_type),
            ],
            'phone_key' => 'nullable|numeric',
            'password' => ['required', 'string', Password::min(8)->mixedCase()->numbers()->symbols()],
            'classroom_id' => request()->register_type === 'student'
                ? 'required|numeric|exists:classrooms,id'
                : 'required|array',
            'classroom_id.*' => request()->register_type === 'teacher'
                ? 'exists:classrooms,id'
                : 'exclude',
            'subject_id' => request()->register_type === 'teacher'
                ? 'required|array'
                : 'exclude',
            'subject_id.*' => request()->register_type === 'teacher'
                ? 'exists:subjects,id'
                : 'exclude'
        ];
    }
}
