<?php

namespace App\Http\Resources\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentCourseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $finishedLessonsCount = $this->finishedLessons()->where('student_id', auth()->user()->id)->count();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'desc' => $this->desc,
            'image' => asset('storage/'.$this->image),
            'duration' => $this->getFormattedDuration(),
            'total_lessons' => $this->sections()->with('lessons')->get()->flatMap(fn ($section) => $section->lessons)->count(),
            'finished_lessons' => $finishedLessonsCount,
        ];
    }

    protected function getFormattedDuration()
    {
        $totalDuration = $this->sections()->with('lessons')->get()->flatMap(fn ($section) => $section->lessons)->sum('duration');

        if ($totalDuration < 60) {
            return "{$totalDuration} ".__('minutes');
        }

        $hours = floor($totalDuration / 60);
        $minutes = $totalDuration % 60;

        if ($minutes > 0) {
            return __('hours', ['hours' => $hours]).' '.__('and').' '.__('minutes', ['minutes' => $minutes]);
        }

        return __('hours', ['hours' => $hours]);
    }
}
