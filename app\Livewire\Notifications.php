<?php

namespace App\Livewire;

use Livewire\Component;

class Notifications extends Component
{
    public $notifications;

    public $count;

    public function mount()
    {
        $this->notifications = auth()->user()->notifications;
        $this->count = count(auth()->user()->notifications);
    }

    public function render()
    {
        return view('livewire.notifications', [
            'notifications' => $this->notifications,
            'count' => $this->count,
        ]);
    }
}
