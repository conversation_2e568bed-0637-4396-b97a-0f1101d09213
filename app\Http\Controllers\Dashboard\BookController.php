<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\BookRequest;
use App\Models\Book;
use App\Models\Classroom;
use App\Models\Subject;
use Illuminate\Support\Facades\Storage;

class BookController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $books = Book::all();

        return view('dashboard.books.index', compact('books'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $classrooms = Classroom::all();
        $subjects = Subject::all();

        return view('dashboard.books.create', compact('classrooms', 'subjects'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BookRequest $request)
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('books/images', 'public');
        }

        if ($request->hasFile('file')) {
            $data['file'] = $request->file('file')->store('books/files', 'public');
        }

        Book::create($data);

        return redirect()->route('admin.books.index')->with('success', transWord('تمت الإضافة بنجاح'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Book $book)
    {
        $classrooms = Classroom::all();
        $subjects = Subject::all();

        return view('dashboard.books.edit', compact('book', 'classrooms', 'subjects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(BookRequest $request, Book $book)
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            if ($book->image) {
                Storage::disk('public')->delete($book->image);
            }

            $data['image'] = $request->file('image')->store('books/images', 'public');
        }

        if ($request->hasFile('file')) {
            if ($book->file) {
                Storage::disk('public')->delete($book->file);
            }

            $data['file'] = $request->file('file')->store('books/files', 'public');
        }

        $book->update($data);

        return redirect()->route('admin.books.index')->with('success', transWord('تم التحديث بنجاح'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Book $book)
    {
        if ($book->image) {
            Storage::disk('public')->delete($book->image);
        }

        if ($book->file) {
            Storage::disk('public')->delete($book->file);
        }

        $book->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }
}
