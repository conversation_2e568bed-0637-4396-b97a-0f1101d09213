<?php

namespace App\Http\Controllers\Api\Student;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Student\SearchRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Student\CourseResource;
use App\Http\Resources\Student\TeacherResource;
use App\Models\Course;
use App\Models\Teacher;

class SearchController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(SearchRequest $request)
    {
        $courses = Course::where(function ($query) use ($request) {
            $query->where('name_ar', 'like', '%'.$request->search.'%')
                ->orWhere('name_en', 'like', '%'.$request->search.'%')
                ->orWhereHas('subject', function ($query) use ($request) {
                    $query->where('name_ar', 'like', '%'.$request->search.'%')
                        ->orWhere('name_en', 'like', '%'.$request->search.'%');
                });
        })->paginate(10, ['*'], 'courses');

        $teachers = Teacher::where(function ($query) use ($request) {
            $query->where('name', 'like', '%'.$request->search.'%')
                ->orWhereHas('subjects', function ($query) use ($request) {
                    $query->where('name_ar', 'like', '%'.$request->search.'%')
                        ->orWhere('name_en', 'like', '%'.$request->search.'%');
                });
        })->paginate(10, ['*'], 'teachers');

        return $this->response([
            'courses' => new BaseCollection($courses, CourseResource::class),
            'teachers' => new BaseCollection($teachers, TeacherResource::class),
        ]);
    }
}
