<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Exam extends Model
{
    use HasFactory;

    protected $fillable = ['name_ar', 'name_en', 'course_id', 'teacher_id', 'duration'];

    public function getNameAttribute()
    {
        return $this->{'name_'.app()->getLocale()};
    }

    public function questions(): HasMany
    {
        return $this->hasMany(Question::class);
    }
}
