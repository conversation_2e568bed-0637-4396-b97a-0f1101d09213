<?php

namespace App\Http\Requests\Dashboard;

use Illuminate\Foundation\Http\FormRequest;

class StudyNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name_ar' => 'required|string|min:3|max:255',
            'name_en' => 'required|string|min:3|max:255',
            'unit_name_ar' => 'required|string|min:3|max:255',
            'unit_name_en' => 'required|string|min:3|max:255',
            'image' => request()->method() === 'PATCH' ? 'sometimes|image|mimes:jpeg,png,jpg|max:512000' : 'required|image|mimes:jpeg,png,jpg|max:512000',
            'file' => request()->method() === 'PATCH' ? 'sometimes|file|mimes:pdf|max:512000' : 'required|file|mimes:pdf|max:512000',
            'price' => 'required|numeric|min:1|min_digits:1|max_digits:10',
            'classroom_id' => 'required|exists:classrooms,id',
            'subject_id' => 'required|exists:subjects,id',
        ];
    }

    public function messages()
    {
        return [
            'image.max' => transWord('The image size must not exceed 500MB.'),
            'file.max' => transWord('The file size must not exceed 500MB.'),
        ];
    }
}
