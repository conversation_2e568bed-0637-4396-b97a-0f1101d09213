<?php

namespace App\Http\Controllers\Api\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Teacher\FileRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Teacher\FileResource;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        $files = $user->files()->paginate(10);

        return $this->response(new BaseCollection($files, FileResource::class));
    }

    public function store(FileRequest $request)
    {
        $user = auth()->user();

        $data = $request->validated();

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $name = time().'_'.$file->getClientOriginalName();
            $path = $file->storeAs('files', $name, 'public');
            $data['path'] = $path;
        }

        $user->files()->create($data);

        return $this->response([], transWord('تم اضافة الملف بنجاح'));
    }

    public function destroy(string $id)
    {
        $file = auth()->user()->files()->find($id);

        if (! $file) {
            return $this->response([], transWord('الملف غير موجود'), 404);
        }

        if ($file->path) {
            Storage::disk('public')->delete($file->path);
        }

        $file->delete();

        return $this->response([], transWord('تم حذف الملف بنجاح'));
    }
}
