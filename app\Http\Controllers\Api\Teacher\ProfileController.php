<?php

namespace App\Http\Controllers\Api\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Teacher\UpdateProfileImageRequest;
use App\Http\Requests\Api\Teacher\UpdateProfileRequest;
use App\Http\Requests\Api\UpdatePasswordRequest;
use App\Http\Resources\Teacher\ProfileResource;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    public function me()
    {
        $teacher = auth()->user();

        return $this->response(new ProfileResource($teacher));
    }

    public function update(UpdateProfileRequest $request)
    {
        $teacher = auth()->user();

        $teacher->update($request->validated());

        $teacher->subjects()->sync($request->subject_id);

        $teacher->classrooms()->sync($request->classroom_id);

        return $this->response(new ProfileResource($teacher), transWord('تم التحديث بنجاح'));
    }

    public function updatePassword(UpdatePasswordRequest $request)
    {
        $teacher = auth()->user();

        if (! \Hash::check($request->old_password, $teacher->password)) {
            return $this->response(null, transWord('كلمة المرور القديمة غير صحيحة'), 422);
        }

        $teacher->update([
            'password' => $request->new_password,
        ]);

        return $this->response(new ProfileResource($teacher), transWord('تم التحديث بنجاح'));
    }

    public function updateImage(UpdateProfileImageRequest $request)
    {
        $teacher = auth()->user();

        $data = $request->validated();

        if ($request->hasFile('image')) {
            if ($teacher->image) {
                Storage::delete($teacher->image);
            }

            $data['image'] = $request->file('image')->store('teachers', 'public');
        } else {
            $data['image'] = $teacher->image;
        }

        $teacher->update($data);

        return $this->response(new ProfileResource($teacher), transWord('تم التحديث بنجاح'));
    }
}
