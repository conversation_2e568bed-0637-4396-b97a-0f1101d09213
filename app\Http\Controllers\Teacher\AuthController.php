<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\AuthRequest;
use App\Http\Requests\Dashboard\ResetRequest;
use App\Mail\ResetPassword;
use App\Models\Teacher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        return \view('teacher.auth.login');
    }

    public function login(AuthRequest $request)
    {
        //attempt to log teacher
        if (Auth::guard('teacher')->attempt(['email' => $request->email, 'password' => $request->password], $request->get('remember'))) {
            return \redirect()->intended(route('teacher.home'))->with('success', __('models.login_success'));
        }

        return redirect()->back()->withInput($request->only('email', 'remember'))->with('error', __('models.login_error'));
    }

    public function logout(Request $request)
    {
        Auth::guard('teacher')->logout();

        return redirect()->route('admin.login');
    }

    public function reset()
    {
        return view('teacher.auth.reset');
    }

    public function sendLink(Request $request)
    {
        $user = Teacher::where('email', $request->email)->first();

        if ($user) {

            $code = \rand(1111, 9999);

            $user->update(['code' => $code]);

            $data = [
                'link' => route('admin.changePassword', $code),
            ];

            // Mail::to($user->email)->send(new ResetPassword($data));

            return redirect()->back()->with('success', __('models.link_sent'));
        } else {

            return redirect()->back()->with('error', __('models.email_not_found'));
        }
    }

    public function changePassword($code)
    {

        $user = Teacher::where('code', $code)->first();

        if ($user) {
            return view('teacher.auth.changePassword', \compact('code'));
        } else {
            return \view('teacher.auth.error');
        }
    }

    public function updatePassword(ResetRequest $request)
    {

        $user = Teacher::where('code', $code)->first();

        if ($user->isVerified == 1) {

            $newPassword = $user->update(['password' => bcrypt($request->password)]);
        }

        if ($newPassword) {

            Auth::guard('teacher')->login($user);

            $user->update(['code' => null]);

            return \redirect(\route('admin.home'))->with('success', __('models.pass_changed'));
        } else {
            return redirect()->back()->with('error', __('models.pass_error'));
        }
    }
}
