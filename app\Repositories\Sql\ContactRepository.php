<?php

namespace App\Repositories\Sql;

use App\Models\Contact;
use App\Repositories\Contract\ContactRepositoryInterface;

class ContactRepository extends BaseRepository implements ContactRepositoryInterface
{
    public function __construct()
    {

        return $this->model = new Contact;
    }

    public function getWhereType($type)
    {
        return $this->model->where('type', $type)->get();
    }
}
