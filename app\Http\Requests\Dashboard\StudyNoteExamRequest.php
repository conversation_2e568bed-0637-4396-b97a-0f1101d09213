<?php

namespace App\Http\Requests\Dashboard;

use Illuminate\Foundation\Http\FormRequest;

class StudyNoteExamRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'study_note_id' => 'required|exists:study_notes,id',
            'duration' => 'required|integer|min:1|max:60',
            'questions' => 'required|array',
            'questions.*.question_ar' => 'required|string|max:255',
            'questions.*.question_en' => 'required|string|max:255',
            'questions.*.image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'questions.*.answers' => 'required|array',
            'questions.*.answers.*.answer_ar' => 'required|string|max:255',
            'questions.*.answers.*.answer_en' => 'required|string|max:255',
            'questions.*.correct_answer' => 'required|integer',
        ];
    }
}
