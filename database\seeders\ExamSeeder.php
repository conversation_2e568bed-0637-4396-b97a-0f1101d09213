<?php

namespace Database\Seeders;

use App\Models\Answer;
use App\Models\Course;
use App\Models\Exam;
use App\Models\Question;
use Illuminate\Database\Seeder;

class ExamSeeder extends Seeder
{
    public function run()
    {
        // Get the first course
        $course = Course::first();

        if (! $course) {
            $this->command->info('No courses found. Please create a course first.');

            return;
        }

        // Create an exam for the first course
        $exam = Exam::create([
            'course_id' => $course->id,
            'teacher_id' => 1,
            'name_ar' => 'اختبار الدورة الأولى',
            'name_en' => 'First Course Exam',
        ]);

        // Create 20 questions for the exam
        for ($i = 1; $i <= 20; $i++) {
            $question = Question::create([
                'exam_id' => $exam->id,
                'question_ar' => "ما هو السؤال رقم $i ?",
                'question_en' => "What is question number $i ?",
            ]);

            // Create 4 answers for each question
            for ($j = 1; $j <= 4; $j++) {
                Answer::create([
                    'question_id' => $question->id,
                    'answer_ar' => "الإجابة $j للسؤال $i",
                    'answer_en' => "Answer $j for question $i",
                    'is_correct' => $j === 1, // Mark the first answer as correct
                ]);
            }
        }

        $this->command->info('Exam with 20 questions and 4 answers per question created successfully!');
    }
}
