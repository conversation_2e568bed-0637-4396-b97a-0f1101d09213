<?php

namespace App\Http\Resources\Student;

use App\Http\Resources\BaseCollection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TeacherResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone_key' => $this->phone_key,
            'phone' => $this->phone,
            'image' => $this->image ? asset('storage/'.$this->image) : asset('app/images/user.png'),
            'isVerified' => (bool) $this->verified,
            'isNotified' => (bool) $this->is_notify,
            'rate' => (string) number_format($this->rates()->avg('rate'), 1) ?? '0.0',
            'rate_count' => (int) $this->rates()->count(),
            'courses_count' => (int) $this->courses()->count(),
            'students_count' => (int) $this->reservations()->where('status', 'accepted')->get()->unique('student_id')->count(),
            'rates' => new BaseCollection($this->rates()->orderBy('id', 'desc')->paginate(10, ['*'], 'rates'), TeacherRateResource::class),
            'courses' => new BaseCollection($this->courses()->orderBy('id', 'desc')->paginate(10, ['*'], 'courses'), CourseResource::class),
        ];
    }
}
