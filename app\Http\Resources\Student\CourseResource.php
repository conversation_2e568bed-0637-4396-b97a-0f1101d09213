<?php

namespace App\Http\Resources\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = auth()->user();

        $isCourseSubscribed = (bool) $user?->courses()->where('course_id', $this->id)->exists();
        $isFavorite = (bool) $user?->favorites()->where('course_id', $this->id)->exists();
        $isCompleted = (bool) $user?->courses()->wherePivot('course_id', $this->id)->wherePivot('is_completed', true)->exists();
        $hasExam = (bool) $this->exam;
        $isExamTaken = (bool) $hasExam ? $user?->examAttempts()->where('exam_id', $this->exam?->id)->exists() : false;

        return array_merge(
            [
                'id' => $this->id,
                'name' => $this->name,
                'desc' => $this->desc,
                'image' => asset('storage/' . $this->image),
                'price' => $this->price,
                'status' => $this->status,
                'subject' => $this->subject->name,
                'teacher' => $this->getTeacherDetails(),
                'duration' => $this->getFormattedDuration(),
                'is_subscribed' => $isCourseSubscribed,
                'is_favorite' => $isFavorite,
                'total_lessons' => $this->sections()->with('lessons')->get()->flatMap(fn($section) => $section->lessons)->count(),
                'is_completed' => $isCompleted,
                'has_exam' => $hasExam,
                'is_exam_taken' => $isExamTaken,
            ],
            $this->when(
                request()->routeIs('courses.show'),
                [
                    'sections' => $this->getSectionsDetails(),
                ],
                []
            )
        );
    }

    protected function getTeacherDetails()
    {
        return [
            'id' => $this->teacher->id,
            'name' => $this->teacher->name,
            'image' => $this->teacher->image ? asset('storage/' . $this->teacher->image) : asset('app/images/user.png'),
            'subject' => $this->teacher?->subject?->name,
        ];
    }

    protected function getFormattedDuration()
    {
        $totalDuration = $this->sections()->with('lessons')->get()->flatMap(fn($section) => $section->lessons)->sum('duration');

        if ($totalDuration < 60) {
            return __('minutes', ['minutes' => $totalDuration]);
        }

        $hours = floor($totalDuration / 60);
        $minutes = $totalDuration % 60;

        if ($minutes > 0) {
            return __('hours', ['hours' => $hours]) . ' ' . __('and') . ' ' . __('minutes', ['minutes' => $minutes]);
        }

        return __('hours', ['hours' => $hours]);
    }

    protected function getSectionsDetails()
    {
        return $this->sections->map(function ($section) {
            return [
                'id' => $section->id,
                'name' => $section->name,
                'total_duration' => $this->formatDuration($section->lessons->sum('duration')),
                'lessons' => $section->lessons->map(function ($lesson) {
                    return [
                        'id' => $lesson->id,
                        'name' => $lesson->name,
                        'duration' => $this->formatDuration($lesson->duration),
                        'is_free' => (bool) $lesson->is_free,
                        'video' => asset('storage/' . $lesson->video),
                        'is_watched' => (bool) auth()->user()?->finishedLessons()->where('lesson_id', $lesson->id)->exists(),
                    ];
                }),
            ];
        });
    }

    protected function formatDuration($duration)
    {
        if ($duration < 60) {
            return "{$duration} minutes";
        }

        $hours = floor($duration / 60);
        $minutes = $duration % 60;

        return $minutes > 0 ? "{$hours} hours and {$minutes} minutes" : "{$hours} hours";
    }
}
