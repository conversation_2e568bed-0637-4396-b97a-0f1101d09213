<?php

namespace App\Http\Controllers\Api\Student;

use App\Models\Course;
use App\Models\Lesson;
use App\Models\Payment;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Student\ExamResource;
use App\Http\Resources\Student\CourseResource;
use App\Http\Requests\Api\Student\FinishLessonRequest;
use App\Http\Requests\Api\Student\SubscripeCourseRequest;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CourseController extends Controller
{
    public function index(Request $request)
    {
        $filters = request()->only(['subject_id']);

        $courses = Course::with(['subject', 'teacher'])->when(isset($filters['subject_id']), function ($query) use ($filters) {
            return $query->where('subject_id', $filters['subject_id']);
        })->paginate(10);

        return $this->response(new BaseCollection($courses, CourseResource::class));
    }

    public function show(string $id)
    {
        $course = Course::with(['subject', 'teacher', 'sections', 'sections.lessons'])->find($id);

        if (! $course) {
            return NotFoundHttpException::class;
        }

        return $this->response(new CourseResource($course));
    }

    public function subscribe(SubscripeCourseRequest $request)
    {
        $student = auth()->user();

        $course = Course::find($request->course_id);

        if (! $course) {
            return $this->response([], transWord('الدورة غير موجودة'), 404);
        }

        if ($student->courses()->where('course_id', $request->course_id)->exists()) {
            return $this->response([], transWord('انت بالفعل مشترك في هذا الدورة'));
        }

        // If course is free (price is 0), add it directly to student's courses
        if ($course->price == 0) {
            $student->courses()->attach($course->id, [
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return $this->response([], transWord('تم الاشتراك في الدورة بنجاح'));
        }

        // For paid courses, create payment record
        $payment = Payment::create([
            'student_id' => $student->id,
            'type' => 'course',
            'related_id' => $course->id,
            'amount' => $course->price,
            'status' => 'pending',
        ]);

        // توليد رابط بوابة الدفع (تحتاج تبنيها حسب توثيق سداد)
        $sadadPaymentUrl = $this->generateSadadPaymentUrl($payment);

        return $this->response([
            'payment_url' => $sadadPaymentUrl,
        ]);
    }

    protected function generateSadadPaymentUrl($payment)
    {
        return url('/pay/') . '/' . $payment->id;
    }

    public function exam(string $id)
    {
        $course = Course::with(['exam', 'exam.questions', 'exam.questions.answers'])->find($id);

        if (! $course) {
            return NotFoundHttpException::class;
        }

        return $this->response(new ExamResource($course->exam));
    }

    public function finishLesson(FinishLessonRequest $request)
    {
        $student = auth()->user();

        $lesson = Lesson::find($request->lesson_id);

        if (! $lesson) {
            abort(404);
        }

        $student->finishedLessons()->firstOrNew([
            'lesson_id' => $lesson->id,
            'course_id' => $lesson->course_id,
        ], [
            'lesson_id' => $lesson->id,
            'course_id' => $lesson->course_id,
        ]);

        return $this->response([], transWord('تم انتهاء الدرس بنجاح'));
    }

    public function completeCourse(string $id)
    {
        $student = auth()->user();

        // Find the course directly in the relationship with the pivot table
        $course = $student->courses()->where('course_id', $id)->first();

        if (! $course) {
            return $this->response([], transWord('الكورس غير موجود'), 404);
        }

        // Update the pivot table column
        $student->courses()->updateExistingPivot($id, ['is_completed' => true]);

        return $this->response([], transWord('تم إتمام الكورس بنجاح'));
    }
}

