<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:clear';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all records from the notifications table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::table('notifications')->truncate();

        $this->info('Notifications table has been cleared.');
    }
}
