<?php

namespace App\Http\Controllers\Api\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Teacher\UpdateReservationRequest;
use App\Http\Resources\Teacher\ReservationResource;

class ReservationController extends Controller
{
    public function index()
    {
        $supportedTypes = [
            'current',
            'done',
        ];

        $type = request()->query('type');

        if (! in_array($type, $supportedTypes)) {
            return $this->response([], 'not supported type', 400);
        }

        $teacher = auth()->user();

        if ($type === 'current') {
            $reservations = $teacher->reservations()->where('status', 'pending')->get();
        } else {
            $reservations = $teacher->reservations()->where('status', '!=', 'pending')->get();
        }

        return $this->response(ReservationResource::collection($reservations));
    }

    public function show(string $id)
    {
        $user = auth()->user();

        $reservation = $user->reservations()->find($id);

        if (! $reservation) {
            return $this->response([], 'reservation not found', 404);
        }

        return $this->response(new ReservationResource($reservation));
    }

    public function update(UpdateReservationRequest $request, string $id)
    {
        $user = auth()->user();

        $reservation = $user->reservations()->find($id);

        if (! $reservation) {
            return $this->response([], 'reservation not found', 404);
        }

        $reservation->update([
            'status' => $request->status,
        ]);

        return $this->response(new ReservationResource($reservation), __('تم التحديث بنجاح'));
    }
}
