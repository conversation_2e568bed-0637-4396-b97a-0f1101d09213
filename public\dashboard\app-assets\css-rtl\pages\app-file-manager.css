.file-manager-application .sidebar-file-manager {
  width : 260px;
  height : 100%;
  background-color : #FFFFFF;
  border-top-right-radius : 0.25rem;
  border-bottom-right-radius : 0.25rem;
  -webkit-transition : all 0.3s ease, background 0s;
          transition : all 0.3s ease, background 0s;
}

.file-manager-application .sidebar-file-manager .sidebar-inner {
  height : inherit;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .dropdown-actions {
  width : 100%;
  padding : 1.5rem 1.5rem 1.8rem;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-close-icon {
  position : absolute;
  top : 0.25rem;
  left : 0.25rem;
  font-size : 1.5rem;
  z-index : 5;
  cursor : pointer;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .add-file-btn ~ .dropdown-menu {
  width : 85%;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .add-file-btn ~ .dropdown-menu:before {
  display : none;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list {
  position : relative;
  height : calc(100% - 85px);
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .my-drive {
  padding : 0.58rem 0;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .my-drive .jstree-node .jstree-icon {
  background-position : 0.5rem;
  background-size : 1.3rem;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .my-drive .jstree-node .jstree-anchor > .jstree-themeicon {
  margin-left : 0.5rem;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .my-drive .jstree-node.jstree-closed > .jstree-icon {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%235e5873\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E');
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .my-drive .jstree-node.jstree-open > .jstree-icon {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%235e5873\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .list-group-labels, .file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .storage-status {
  margin-top : 2.2rem;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .list-group-item {
  padding : 0.58rem 1.9rem;
  font-weight : 500;
  border : 0;
  border-radius : 0;
  border-right : 2px solid transparent;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .list-group-item:hover {
  z-index : 0 !important;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .list-group-item + .list-group-item.active {
  margin-top : 0;
}

.file-manager-application .sidebar-file-manager .sidebar-inner .sidebar-list .active {
  background-color : transparent;
  border-color : #7367F0;
  color : #7367F0;
}

.file-manager-application .sidebar-file-manager.show {
  -webkit-transition : all 0.25s ease, background 0s !important;
          transition : all 0.25s ease, background 0s !important;
  -webkit-transform : translateX(0) !important;
      -ms-transform : translateX(0) !important;
          transform : translateX(0) !important;
  z-index : 10;
}

.file-manager-application .content-area-wrapper {
  border : 1px solid #EBE9F1;
  border-radius : 0.25rem;
}

.file-manager-application .content-area-wrapper .content-right .content-wrapper {
  padding : 0;
}

.file-manager-application .content-area-wrapper .file-manager-main-content {
  border-right : 1px solid #EBE9F1;
  height : inherit;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-header {
  padding : 0.4rem 1rem;
  border-bottom : 1px solid #EBE9F1;
  background-color : #FFFFFF;
  border-top-left-radius : 0.357rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-header .file-manager-toggler {
  cursor : pointer;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-header input {
  border-color : transparent;
  box-shadow : none;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-header .file-actions:not(.show) {
  display : none;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body {
  position : relative;
  padding : 1.5rem 1.5rem 0;
  height : calc(100% - 50px);
  background-color : #FFFFFF;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-flex-wrap : wrap;
      -ms-flex-wrap : wrap;
          flex-wrap : wrap;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item {
  border : 1px solid #EBE9F1;
  margin-bottom : 1.6rem;
  box-shadow : none;
  margin-left : 1rem;
  -webkit-transition : none;
          transition : none;
  cursor : pointer;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .card-body {
  padding : 1rem;
  padding-bottom : 0.5rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .custom-control-input:not(:checked) ~ .custom-control-label:before {
  background-color : transparent;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .file-logo-wrapper {
  padding : 1rem;
  height : 7.5rem;
  background-color : #F8F8F8;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .file-logo-wrapper .feather-folder {
  stroke : #BABFC7;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .dropdown-menu {
  -webkit-transform : none;
      -ms-transform : none;
          transform : none;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .dropdown-menu:before {
  display : none;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .content-wrapper {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-orient : horizontal;
  -webkit-box-direction : normal;
  -webkit-flex-direction : row;
      -ms-flex-direction : row;
          flex-direction : row;
  -webkit-box-pack : justify;
  -webkit-justify-content : space-between;
  -ms-flex-pack : justify;
          justify-content : space-between;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
  height : auto;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .file-date {
  -webkit-box-flex : 1;
  -webkit-flex-grow : 1;
  -ms-flex-positive : 1;
          flex-grow : 1;
  margin-bottom : 0;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-manager-item.selected {
  border-color : #7367F0;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .file-name {
  width : calc(100% - 26rem);
  min-height : 1rem;
  font-weight : 600;
  -webkit-box-flex : 1;
  -webkit-flex-grow : 1;
  -ms-flex-positive : 1;
          flex-grow : 1;
  overflow : hidden;
  text-overflow : ellipsis;
  white-space : nowrap;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container .files-section-title {
  width : 100%;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view {
  -webkit-box-orient : vertical;
  -webkit-box-direction : normal;
  -webkit-flex-direction : column;
      -ms-flex-direction : column;
          flex-direction : column;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .files-section-title {
  display : none;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .files-header {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-pack : justify;
  -webkit-justify-content : space-between;
  -ms-flex-pack : justify;
          justify-content : space-between;
  margin-right : 7.2rem;
  margin-bottom : 1rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .files-header p {
  font-weight : 600;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .files-header .file-last-modified, .file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .files-header .file-item-size {
  margin-left : 3rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item {
  -webkit-box-orient : horizontal;
  -webkit-box-direction : normal;
  -webkit-flex-direction : row;
      -ms-flex-direction : row;
          flex-direction : row;
  -webkit-box-flex : 0;
  -webkit-flex : 0 0 100%;
      -ms-flex : 0 0 100%;
          flex : 0 0 100%;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
  max-width : 100%;
  margin-bottom : 0.75rem;
  margin-left : 0;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item .file-logo-wrapper {
  padding-left : 0;
  width : auto;
  height : auto;
  background-color : transparent !important;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item .file-logo-wrapper img {
  height : 1.5rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item .file-logo-wrapper .feather-folder, .file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item .file-logo-wrapper .feather-arrow-up {
  width : 19px;
  height : 18px;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item .file-logo-wrapper .dropdown {
  position : absolute;
  left : 1rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item .custom-checkbox {
  margin : 0 1.75rem 0 0.4rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item .file-accessed {
  display : none;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .file-manager-item .file-size {
  width : 5.71rem;
  -webkit-box-flex : 1;
  -webkit-flex-grow : 1;
  -ms-flex-positive : 1;
          flex-grow : 1;
  margin-bottom : 0;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container.list-view .folder.level-up .file-logo-wrapper {
  margin-right : 3.5rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .files-header {
  display : none;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item .content-wrapper {
  margin-bottom : 0rem;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item .file-date {
  display : none;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item .file-size {
  color : #B9B9C3;
  font-size : 85%;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item:not(.selected):not(:hover) .custom-checkbox, .file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item:not(.selected):not(:hover) .toggle-dropdown {
  opacity : 0;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item .feather-folder {
  height : 32px;
  width : 35px;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item.folder.level-up {
  display : none !important;
}

.file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item .custom-checkbox {
  position : absolute;
  top : 1rem;
  right : 1rem;
}

@media screen and (max-width: 1200px) {
  .file-manager-application .content-right {
    width : 100%;
  }
  .file-manager-application .content-body {
    margin-right : 0 !important;
  }
  .file-manager-application .content-area-wrapper .file-manager-main-content {
    border-right : 0;
  }
  .file-manager-application .sidebar-left .sidebar {
    z-index : 10;
  }
  .file-manager-application .sidebar-left .sidebar .sidebar-file-manager {
    -webkit-transform : translateX(112%);
        -ms-transform : translateX(112%);
            transform : translateX(112%);
    -webkit-transition : all 0.25s ease;
            transition : all 0.25s ease;
    position : absolute;
  }
}

@media (max-width: 767.98px) {
  .file-manager-application .view-container .file-manager-item {
    -webkit-box-flex : 47%;
    -webkit-flex : 47%;
        -ms-flex : 47%;
            flex : 47%;
  }
}

@media (max-width: 575.98px) {
  .file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-header {
    padding-right : 0.5rem;
  }
  .file-manager-application .content-area-wrapper .file-manager-main-content .file-manager-content-header .file-actions .dropdown {
    padding-right : 0.5rem;
    padding-left : 0.5rem;
  }
  .file-manager-application .content-area-wrapper .view-container .file-manager-item {
    -webkit-box-flex : 0;
    -webkit-flex : 0 0 100%;
        -ms-flex : 0 0 100%;
            flex : 0 0 100%;
    max-width : 100%;
  }
  .file-manager-application .content-area-wrapper .view-container.list-view .file-date, .file-manager-application .content-area-wrapper .view-container.list-view .file-last-modified, .file-manager-application .content-area-wrapper .view-container.list-view .file-size, .file-manager-application .content-area-wrapper .view-container.list-view .file-item-size {
    display : none !important;
  }
}

.file-manager-application .view-container .file-manager-item {
  -webkit-box-flex : 0;
  -webkit-flex : 0 0 47%;
      -ms-flex : 0 0 47%;
          flex : 0 0 47%;
  max-width : 50%;
}

@media (min-width: 576px) {
  .file-manager-application .view-container .file-manager-item {
    -webkit-box-flex : 0;
    -webkit-flex : 0 0 23%;
        -ms-flex : 0 0 23%;
            flex : 0 0 23%;
    max-width : 25%;
  }
}