<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\StudyNoteRequest;
use App\Models\Classroom;
use App\Models\StudyNote;
use App\Models\Subject;
use Illuminate\Support\Facades\Storage;

class StudyNoteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $notes = StudyNote::all();

        return view('dashboard.study_notes.index', compact('notes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $classrooms = Classroom::all();
        $subjects = Subject::all();

        return view('dashboard.study_notes.create', compact('classrooms', 'subjects'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StudyNoteRequest $request)
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('study_notes/images', 'public');
        }

        if ($request->hasFile('file')) {
            $data['file'] = $request->file('file')->store('study_notes/files', 'public');
        }

        StudyNote::create($data);

        return redirect()->route('admin.study_notes.index')->with('success', transWord('تمت الإضافة بنجاح'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StudyNote $studyNote)
    {
        $classrooms = Classroom::all();
        $subjects = Subject::all();

        return view('dashboard.study_notes.edit', compact('studyNote', 'classrooms', 'subjects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StudyNoteRequest $request, StudyNote $studyNote)
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            if ($studyNote->image) {
                Storage::disk('public')->delete($studyNote->image);
            }
            $data['image'] = $request->file('image')->store('study_notes/images', 'public');
        }

        if ($request->hasFile('file')) {
            if ($studyNote->file) {
                Storage::disk('public')->delete($studyNote->file);
            }
            $data['file'] = $request->file('file')->store('study_notes/files', 'public');
        }

        $studyNote->update($data);

        return redirect()->route('admin.study_notes.index')->with('success', transWord('تم التحديث بنجاح'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StudyNote $studyNote)
    {
        if ($studyNote->image) {
            Storage::disk('public')->delete($studyNote->image);
        }

        if ($studyNote->file) {
            Storage::disk('public')->delete($studyNote->file);
        }

        $studyNote->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }
}
