<?php

namespace App\Http\Controllers\Api;

use App\Mail\ResetCode;
use App\Models\Student;
use App\Models\Teacher;
use Illuminate\Http\Request;
use App\Mail\EmailVerification;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\Api\LoginRequest;
use App\Http\Resources\StudentResource;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\Api\RegisterRequest;
use App\Notifications\StudentNotification;
use App\Notifications\TeacherNotification;
use App\Http\Requests\Api\ResetPasswordRequest;
use App\Http\Resources\Teacher\TeacherResource;
use App\Http\Requests\Api\ForgetPasswordRequest;
use App\Http\Requests\Api\VerifyResetCodeRequest;

class AuthController extends Controller
{
    public function register(RegisterRequest $request)
    {
        // Start a transaction
        DB::beginTransaction();

        try {
            $credentials = $request->validated();

            $credentials['verified'] = true;

            if ($request->register_type === 'student') {

                $student = Student::create($credentials);

                $token = $student->createToken('auth_token', ['student'])->plainTextToken;

                $student->updateFirebaseToken($request->device_id, $request->firebase_token);

                $notificationData = [
                    'title_ar' => 'تم إنشاء الحساب بنجاح!',
                    'title_en' => 'You have successfully registered your account!',
                    'body_ar' => 'تم إنشاء حسابك بنجاح!',
                    'body_en' => 'You have successfully registered your account!',
                    'icon' => asset('app/images/notifications/register.png'),
                ];

                $student->notify(new StudentNotification($notificationData));

                // $code = 1234; //rand(1000, 9999);

                // $student->update([
                //     'code' => $code,
                // ]);

                // Mail::to($student->email)->send(new EmailVerification($student->name, $code));

                // Commit the transaction for student registration
                DB::commit();

                return $this->response([
                    'token' => $token,
                    'user' => new StudentResource($student),
                ], transWord('تم إنشاء حسابك بنجاح'));
            } else {
                $teacher = Teacher::create($credentials);

                if (isset($credentials['subject_id'])) {
                    $teacher->subjects()->sync($credentials['subject_id']);
                }

                if (isset($credentials['classroom_id'])) {
                    $teacher->classrooms()->sync($credentials['classroom_id']);
                }

                $token = $teacher->createToken('auth_token', ['teacher'])->plainTextToken;

                $teacher->updateFirebaseToken($request->device_id, $request->firebase_token);

                $notificationData = [
                    'title_ar' => 'تم إنشاء الحساب بنجاح!',
                    'title_en' => 'You have successfully registered your account!',
                    'body_ar' => 'تم إنشاء حسابك بنجاح!',
                    'body_en' => 'You have successfully registered your account!',
                    'icon' => asset('app/images/notifications/register.png'),
                ];

                $teacher->notify(new TeacherNotification($notificationData));

                // $code = 1234; // rand(1000, 9999);

                // $teacher->update([
                //     'code' => $code,
                // ]);

                // Mail::to($teacher->email)->send(new EmailVerification($teacher->name, $code));

                // Commit the transaction for teacher registration
                DB::commit();

                return $this->response([
                    'token' => $token,
                    'user' => new TeacherResource($teacher),
                ], transWord('تم إنشاء حسابك بنجاح'));
            }
        } catch (\Exception $e) {
            // Rollback the transaction if any error occurs
            DB::rollBack();
            logger($e->getMessage());

            // Optionally, log the error or return a custom message
            return $this->response(null, 'Registration failed, please try again later.', 500);
        }
    }

    public function login(LoginRequest $request)
    {
        $credentials = $request->only('email', 'password');

        if ($request->register_type === 'student') {

            $student = Student::where('email', $credentials['email'])->first();

            if (! $student) {
                return $this->response(null, transWord('البريد الالكتروني غير موجود'), 404);
            }

            if (! Hash::check($credentials['password'], $student->password)) {
                return $this->response(null, transWord('البريد الالكترونى أو كلمة المرور غير صحيح'), 404);
            } else {

                $token = $student->createToken('auth_token', ['student'])->plainTextToken;

                $student->updateFirebaseToken($request->device_id, $request->firebase_token);

                return $this->response([
                    'token' => $token,
                    'user' => new StudentResource($student),
                ], transWord('تم تسجيل الدخول بنجاح'));
            }
        } else {
            $teacher = Teacher::where('email', $credentials['email'])->first();

            if (! $teacher) {
                return $this->response(null, transWord('البريد الالكتروني غير موجود'), 404);
            }

            if (! Hash::check($credentials['password'], $teacher->password)) {
                return $this->response(null, transWord('كلمة المرور غير صحيحة'), 404);
            } else {

                $token = $teacher->createToken('auth_token', ['teacher'])->plainTextToken;

                $teacher->updateFirebaseToken($request->device_id, $request->firebase_token);

                return $this->response([
                    'token' => $token,
                    'user' => new TeacherResource($teacher),
                ], transWord('تم تسجيل الدخول بنجاح'));
            }
        }
    }

    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();

        return $this->response(null, transWord('تم تسجيل الخروج بنجاح'));
    }

    public function forgetPassword(ForgetPasswordRequest $request)
    {

        if ($request->register_type === 'student') {

            $student = Student::where('email', $request->email)->first();

            if (! $student) {
                return $this->response(null, transWord('البريد الالكتروني غير موجود'), 404);
            }

            $code = 1234; //rand(1000, 9999);

            $student->update([
                'code' => $code,
            ]);

            if (! $student->verified) {
                Mail::to($student->email)->send(new EmailVerification($student->name, $code));
            } else {
                Mail::to($student->email)->send(new ResetCode($student->name, $code));
            }
        } else {

            $teacher = Teacher::where('email', $request->email)->first();

            if (! $teacher) {
                return $this->response(null, transWord('البريد الالكتروني غير موجود'), 404);
            }

            $code = rand(1000, 9999);

            $teacher->update([
                'code' => $code,
            ]);

            if (! $teacher->verified) {
                Mail::to($student->email)->send(new EmailVerification($teacher->name, $code));
            } else {
                Mail::to($teacher->email)->send(new ResetCode($teacher->name, $code));
            }
        }

        return $this->response([
            'code' => $code,
        ], transWord('تم ارسال كود التحقق بنجاح'));
    }

    public function verifyResetCode(VerifyResetCodeRequest $request)
    {
        if ($request->register_type === 'student') {

            $student = Student::where('email', $request->email)->first();

            if (! $student) {
                return $this->response(null, transWord('البريد الالكتروني غير موجود'), 404);
            }

            if ($student->code != $request->code) {
                return $this->response(null, transWord('كود التحقق غير صحيح'), 404);
            }

            $student->update([
                'code' => null,
                'verified' => true,
            ]);
        } else {

            $teacher = Teacher::where('email', $request->email)->first();

            if (! $teacher) {
                return $this->response(null, transWord('البريد الالكتروني غير موجود'), 404);
            }

            if ($teacher->code != $request->code) {
                return $this->response(null, transWord('كود التحقق غير صحيح'), 404);
            }

            $teacher->update([
                'code' => null,
                'verified' => true,
            ]);
        }

        return $this->response(null, transWord('تم التحقق من الحساب'));
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        if ($request->register_type === 'student') {

            $student = Student::where('email', $request->email)->first();

            if (! $student) {
                return $this->response(null, transWord('البريد الالكتروني غير موجود'), 404);
            }

            $student->update([
                'password' => $request->new_password,
            ]);
        } else {

            $teacher = Teacher::where('email', $request->email)->first();

            if (! $teacher) {
                return $this->response(null, transWord('البريد الالكتروني غير موجود'), 404);
            }

            $teacher->update([
                'password' => $request->new_password,
            ]);
        }

        return $this->response(null, transWord('تم تغيير كلمة المرور بنجاح'));
    }

    public function deleteAccount(Request $request)
    {
        $user = auth()->user();

        if ($user->image) {
            Storage::delete($user->image);
        }

        $user->tokens()->delete();

        $user->delete();

        return $this->response(null, transWord('تم حذف الحساب بنجاح'));
    }
}
