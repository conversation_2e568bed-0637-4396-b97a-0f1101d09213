<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Plan::insert([
            [
                'name_en' => 'Basic Plan',
                'name_ar' => 'خطة أساسية',
                'features' => json_encode([
                    'en' => ['Feature 1', 'Feature 2'],
                    'ar' => ['الميزة 1', 'الميزة 2'],
                ]),
                'price' => 9.99,
                'duration_in_months' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name_en' => 'Premium Plan',
                'name_ar' => 'خطة مميزة',
                'features' => json_encode([
                    'en' => ['Feature A', 'Feature B'],
                    'ar' => ['الميزة A', 'الميزة B'],
                ]),
                'price' => 19.99,
                'duration_in_months' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name_en' => 'Enterprise Plan',
                'name_ar' => 'خطة عامة',
                'features' => json_encode([
                    'en' => ['Feature C', 'Feature D'],
                    'ar' => ['الميزة C', 'الميزة D'],
                ]),
                'price' => 49.99,
                'duration_in_months' => 12,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
