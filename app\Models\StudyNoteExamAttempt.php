<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudyNoteExamAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'study_note_exam_id',
        'score',
    ];

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function exam()
    {
        return $this->belongsTo(StudyNoteExam::class, 'study_note_exam_id');
    }
}
