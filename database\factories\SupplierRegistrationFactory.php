<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SupplierRegistration>
 */
class SupplierRegistrationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'supplier_name' => $this->faker->name,
            'commercial_registration_number' => $this->faker->ean8,
            'email' => $this->faker->safeEmail,
            'phone' => $this->faker->phoneNumber,
            'activity_classification' => $this->faker->word,
        ];
    }
}
