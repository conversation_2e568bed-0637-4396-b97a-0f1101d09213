<?php

namespace App\Http\Controllers\Api\Student;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Student\SubmitExamRequest;
use App\Http\Resources\Student\ExamPointResource;
use App\Models\Exam;
use App\Models\ExamAttempt;
use Illuminate\Support\Facades\DB;

class ExamController extends Controller
{
    public function submit(SubmitExamRequest $request, string $id)
    {
        $exam = Exam::find($id);

        if (! $exam) {
            return $this->response(null, transWord('الامتحان غير موجود'), 404);
        }

        $student = auth()->user();

        $score = 0;

        // Calculate score
        foreach ($request->answers as $response) {
            $question = $exam->questions()->findOrFail($response['question_id']);
            $answer = $question->answers()->findOrFail($response['answer_id']);

            // Increment score if the answer is correct
            if ($answer->is_correct) {
                $score++;
            }
        }

        $student->examAttempts()->create([
            'exam_id' => $exam->id,
            'score' => $score,
        ]);

        return $this->response([
            'total_questions' => $exam->questions->count(),
            'correct_answers' => $score,
            'final_score' => "{$score}/{$exam->questions->count()}",
        ], transWord('تم تسجيل الامتحان بنجاح'));
    }

    public function points()
    {
        $weekPoints = ExamAttempt::select('student_id', DB::raw('SUM(score) as total_points'))
            ->with('student') // Load student relationship
            ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]) // Filter by current week
            ->groupBy('student_id') // Group by student_id
            ->orderBy('total_points', 'desc') // Order by total points
            ->get();

        // Monthly points per student
        $monthPoints = ExamAttempt::select('student_id', DB::raw('SUM(score) as total_points'))
            ->with('student') // Load student relationship
            ->whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()]) // Filter by current month
            ->groupBy('student_id') // Group by student_id
            ->orderBy('total_points', 'desc') // Order by total points
            ->get();

        return $this->response([
            'week_points' => ExamPointResource::collection($weekPoints),
            'month_points' => ExamPointResource::collection($monthPoints),
        ]);
    }
}
