<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\SubjectRequest;
use App\Models\Subject;

class SubjectController extends Controller
{
    public function index()
    {
        $subjects = Subject::all();

        return view('dashboard.subjects.index', compact('subjects'));
    }

    public function create()
    {
        return view('dashboard.subjects.create');
    }

    public function store(SubjectRequest $request)
    {
        $data = $request->validated();

        Subject::create($data);

        return redirect()->route('admin.subjects.index')->with('success', transWord('تمت الإضافة بنجاح'));
    }

    public function show(string $id)
    {
        //
    }

    public function edit(Subject $subject)
    {
        return view('dashboard.subjects.edit', compact('subject'));
    }

    public function update(SubjectRequest $request, Subject $subject)
    {
        $data = $request->validated();

        $subject->update($data);

        return redirect()->route('admin.subjects.index')->with('success', transWord('تم التحديث بنجاح'));
    }

    public function destroy(Subject $subject)
    {
        $subject->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }
}
