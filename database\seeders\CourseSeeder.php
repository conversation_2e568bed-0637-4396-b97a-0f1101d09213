<?php

namespace Database\Seeders;

use App\Models\Course;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $course = Course::create([
            'name_ar' => 'رياضيات',
            'name_en' => 'Math',
            'subject_id' => 1,
            'classroom_id' => 1,
            'teacher_id' => 1,
            'desc_ar' => 'هذا النص هو مثال لنص يمكن ان يستبدل في نفس المساحة',
            'desc_en' => 'This is a sample text that can be replaced in the same space',
            'price' => 100,
            'image' => 'courses/course.png',
        ]);

        // Create a Section associated with the Course
        $section = $course->sections()->create([
            'name_ar' => 'الوحدة الأولي - المقدمة',
            'name_en' => 'First Unit - Intro',
        ]);

        // Create a Lesson associated with the Section and the Course
        $section->lessons()->create([
            'name_ar' => 'المحاضرة الاولى',
            'name_en' => 'First lecture',
            'course_id' => $course->id,
            'video' => "courses/lessons/{$course->id}/video.mp4",
            'duration' => 10,
        ]);
    }
}
