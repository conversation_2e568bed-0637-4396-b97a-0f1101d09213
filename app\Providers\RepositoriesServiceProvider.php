<?php

namespace App\Providers;

use App\Repositories\Contract\ArticleRepositoryInterface;
use App\Repositories\Contract\BaseRepositoryInterface;
use App\Repositories\Contract\BranchRepositoryInterface;
use App\Repositories\Contract\CompanyRepositoryInterface;
use App\Repositories\Contract\ContactRepositoryInterface;
use App\Repositories\Contract\DepartmentRepositoryInterface;
use App\Repositories\Contract\FaqRepositoryInterface;
use App\Repositories\Contract\NewsSubscriptionRepositoryInterface;
use App\Repositories\Contract\OrderServiceRepositoryInterface;
use App\Repositories\Contract\RecruitmentRepositoryInterface;
use App\Repositories\Contract\ServiceRepositoryInterface;
use App\Repositories\Contract\SettingRepositoryInterface;
use App\Repositories\Contract\SupplierRegistrationRepositoryInterface;
use App\Repositories\Contract\UserRepositoryInterface;
use App\Repositories\Sql\ArticleRepository;
use App\Repositories\Sql\BaseRepository;
use App\Repositories\Sql\BranchRepository;
use App\Repositories\Sql\CompanyRepository;
use App\Repositories\Sql\ContactRepository;
use App\Repositories\Sql\DepartmentRepository;
use App\Repositories\Sql\FaqRepository;
use App\Repositories\Sql\NewsSubscriptionRepository;
use App\Repositories\Sql\OrderServiceRepository;
use App\Repositories\Sql\RecruitmentRepository;
use App\Repositories\Sql\ServiceRepository;
use App\Repositories\Sql\SettingRepository;
// interface

// repository
use App\Repositories\Sql\SupplierRegistrationRepository;
use App\Repositories\Sql\UserRepository;
use Illuminate\Support\ServiceProvider;

class RepositoriesServiceProvider extends ServiceProvider
{
    public function register()
    {

        $this->app->bind(NewsSubscriptionRepositoryInterface::class, NewsSubscriptionRepository::class);

        $this->app->bind(SupplierRegistrationRepositoryInterface::class, SupplierRegistrationRepository::class);

        $this->app->bind(RecruitmentRepositoryInterface::class, RecruitmentRepository::class);

        $this->app->bind(OrderServiceRepositoryInterface::class, OrderServiceRepository::class);

        $this->app->bind(FaqRepositoryInterface::class, FaqRepository::class);

        $this->app->bind(BranchRepositoryInterface::class, BranchRepository::class);

        $this->app->bind(DepartmentRepositoryInterface::class, DepartmentRepository::class);

        $this->app->bind(CompanyRepositoryInterface::class, CompanyRepository::class);

        $this->app->bind(ArticleRepositoryInterface::class, ArticleRepository::class);

        $this->app->bind(ServiceRepositoryInterface::class, ServiceRepository::class);

        $this->app->bind(ContactRepositoryInterface::class, ContactRepository::class);

        $this->app->bind(SettingRepositoryInterface::class, SettingRepository::class);

        $this->app->bind(UserRepositoryInterface::class, UserRepository::class);

        $this->app->bind(BaseRepositoryInterface::class, BaseRepository::class);
    }

    public function boot()
    {
        //
    }
}
