<?php

namespace App\Http\Controllers\Api\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Resources\Teacher\StudentResource;

class StudentController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke()
    {
        $teacher = auth()->user();

        $students = $teacher->reservations()
            ->where('status', 'accepted')
            ->with('student')
            ->get()
            ->pluck('student');

        return $this->response(StudentResource::collection($students));
    }
}
