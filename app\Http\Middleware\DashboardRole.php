<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DashboardRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        $user = auth()->user();

        if ($user && $user->hasAnyRole($roles)) {
            if ($user->isAccountVerified) {
                return $next($request);
            } else {
                return redirect()->back()->with('error', transWord('هذا الحساب غير مفعل'));
            }
        }

        return abort(404);
    }
}
