<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'price',
        'features',
        'duration_in_months',
    ];

    public function getNameAttribute()
    {
        return $this->{'name_'.app()->getLocale()};
    }

    public function getPlanFeaturesAttribute()
    {
        $features = json_decode($this->features, true);

        return $features[app()->getLocale()];
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }
}
