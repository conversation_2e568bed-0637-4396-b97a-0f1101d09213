<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Course;
use Illuminate\Http\JsonResponse;

class CourseController extends Controller
{
    public function index()
    {
        $courses = Course::all();

        return view('dashboard.courses.index', compact('courses'));
    }

    public function show(string $id)
    {
        $course = Course::with(['teacher', 'subject', 'sections', 'lessons'])->findOrFail($id);

        return view('dashboard.courses.show', compact('course'));
    }

    public function destroy(string $id)
    {
        //
    }

    public function activeCourse(Course $course): JsonResponse
    {
        $course->update([
            'status' => $course->status === 'active' ? 'disabled' : 'active',
        ]);

        if ($course->status === 'active') {
            return response()->json([
                'message' => transWord('تم تفعيل الكورس'),
                'verified' => true,
                'textValue' => transWord('مفعل'),
            ]);
        } else {
            return response()->json([
                'message' => transWord('تم إلغاء تفعيل الكورس'),
                'verified' => false,
                'textValue' => transWord('غير مفعل'),
            ]);
        }
    }
}
