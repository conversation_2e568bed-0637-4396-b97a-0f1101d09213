<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Answer extends Model
{
    use HasFactory;

    protected $fillable = ['answer_ar', 'answer_en', 'question_id', 'is_correct'];

    public function getAnswerAttribute()
    {
        return $this->{'answer_'.app()->getLocale()};
    }

    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class);
    }
}
