<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\ClassroomRequest;
use App\Models\Classroom;

class ClassroomController extends Controller
{
    public function index()
    {
        $classrooms = Classroom::all();

        return view('dashboard.classrooms.index', compact('classrooms'));
    }

    public function create()
    {
        return view('dashboard.classrooms.create');
    }

    public function store(ClassroomRequest $request)
    {
        $data = $request->validated();

        Classroom::create($data);

        return redirect()->route('admin.classrooms.index')->with('success', transWord('تمت الإضافة بنجاح'));
    }

    public function show(string $id)
    {
        //
    }

    public function edit(Classroom $classroom)
    {
        return view('dashboard.classrooms.edit', compact('classroom'));
    }

    public function update(ClassroomRequest $request, Classroom $classroom)
    {
        $data = $request->validated();

        $classroom->update($data);

        return redirect()->route('admin.classrooms.index')->with('success', transWord('تم التحديث بنجاح'));
    }

    public function destroy(Classroom $classroom)
    {
        $classroom->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }
}
