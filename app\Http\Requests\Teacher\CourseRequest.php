<?php

namespace App\Http\Requests\Teacher;

use Illuminate\Foundation\Http\FormRequest;

class CourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name_ar' => 'required|string|min:3|max:255',
            'name_en' => 'required|string|min:3|max:255',
            'desc_ar' => 'required|string|min:3',
            'desc_en' => 'required|string|min:3',
            'subject_id' => 'required|exists:subjects,id',
            'classroom_id' => 'required|exists:classrooms,id',
            'image' => request()->method() === 'PATCH' ? 'sometimes|image|mimes:jpeg,png,jpg|max:5120' : 'required|image|mimes:jpeg,png,jpg|max:5120',
            'price' => 'required|numeric|min:1',
            'sections' => request()->method() === 'PATCH' ? 'nulable' : 'required|array|min:1',
            'sections.*.ar' => request()->method() === 'PATCH' ? 'nulable' : 'required|string|min:3|max:255',
            'sections.*.en' => request()->method() === 'PATCH' ? 'nulable' : 'required|string|min:3|max:255',
        ];
    }
}
