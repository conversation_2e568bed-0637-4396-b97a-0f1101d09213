<?php

namespace App\Http\Resources\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->itemable->name, // Dynamically access the polymorphic relation
            'image' => asset('storage/' . $this->itemable->image),
            'price' => $this->price,
            'type' => class_basename($this->itemable), // Returns "Book" or "Note"
        ];
    }
}
