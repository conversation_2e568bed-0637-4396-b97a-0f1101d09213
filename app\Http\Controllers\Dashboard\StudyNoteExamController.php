<?php

namespace App\Http\Controllers\Dashboard;

use App\Models\Exam;
use App\Models\Answer;
use App\Models\Question;
use App\Models\StudyNote;
use App\Models\StudyNoteExam;
use App\Models\StudyNoteAnswer;
use App\Models\StudyNoteQuestion;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\Dashboard\StudyNoteExamRequest;

class StudyNoteExamController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $exams = StudyNoteExam::all();

        return view('dashboard.exams.index', compact('exams'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $notes = StudyNote::all();

        return view('dashboard.exams.create', compact('notes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StudyNoteExamRequest $request)
    {
        $validated = $request->validated();

        // Check if the teacher already has an exam for the same course
        $existingExam = StudyNoteExam::where('study_note_id', $validated['study_note_id'])
            ->first();

        if ($existingExam) {
            return redirect()->back()
                ->with('error', transWord('الملزمة لديه اختبار بالفعل'))
                ->withInput();
        }

        try {
            // Start transaction
            DB::beginTransaction();

            // Create the exam
            $studyNoteExam = StudyNoteExam::create([
                'name_ar' => $validated['name_ar'],
                'name_en' => $validated['name_en'],
                'study_note_id' => $validated['study_note_id'],
                'duration' => $validated['duration'],
            ]);

            // Loop through the questions and save them
            foreach ($validated['questions'] as $questionData) {
                // Handle image upload
                $imagePath = null;

                if (isset($questionData['image'])) {
                    // Store the image in the 'public/storage/questions' directory
                    $imagePath = $questionData['image']->store('questions', 'public');
                }

                // Create the question
                $question = StudyNoteQuestion::create([
                    'study_note_exam_id' => $studyNoteExam->id,
                    'question_ar' => $questionData['question_ar'],
                    'question_en' => $questionData['question_en'],
                    'image' => $imagePath, // Save the image path
                ]);

                // Create the answers for this question
                foreach ($questionData['answers'] as $index => $answerData) {
                    $isCorrect = ($index == $questionData['correct_answer']);
                    StudyNoteAnswer::create([
                        'study_note_question_id' => $question->id,
                        'answer_ar' => $answerData['answer_ar'],
                        'answer_en' => $answerData['answer_en'],
                        'is_correct' => $isCorrect,
                    ]);
                }
            }

            // Commit transaction
            DB::commit();

            // Redirect after successful creation
            return redirect()->route('admin.study_note_exams.index')->with('success', transWord('تمت الإضافة بنجاح'));
        } catch (\Exception $e) {
            // Rollback transaction on failure
            DB::rollBack();

            // Log the error (optional)
            Log::error('Error creating exam:', ['error' => $e->getMessage()]);

            // Redirect back with an error message
            return redirect()->back()
                ->withErrors(['error' => transWord('حدث خطأ أثناء إضافة الاختبار. الرجاء المحاولة لاحقًا')])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StudyNoteExam $studyNoteExam)
    {
        $notes = StudyNote::all();

        return view('dashboard.exams.edit', compact('studyNoteExam', 'notes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StudyNoteExamRequest $request, StudyNoteExam $studyNoteExam)
    {
        $validated = $request->validated();

        // Check if another exam already exists for the same study note
        $existingExam = StudyNoteExam::where('study_note_id', $validated['study_note_id'])
            ->where('id', '!=', $studyNoteExam->id)
            ->exists();

        if ($existingExam) {
            return redirect()->back()
                ->withErrors(['study_note_id' => transWord('الملزمة لديه اختبار بالفعل')])
                ->withInput();
        }

        try {
            // Start transaction
            DB::beginTransaction();

            // Update exam details
            $studyNoteExam->update([
                'name_ar' => $validated['name_ar'],
                'name_en' => $validated['name_en'],
                'study_note_id' => $validated['study_note_id'],
                'duration' => $validated['duration'],
            ]);

            // Check if questions exist in the request
            if ($request->has('questions')) {
                $questionIds = [];

                foreach ($request->questions as $questionIndex => $questionData) {

                    // Check if the question already exists
                    if (isset($questionData['id'])) {
                        $question = StudyNoteQuestion::findOrFail($questionData['id']);

                        if (isset($questionData['image'])) {
                            // Delete the old image
                            if ($question->image) {
                                Storage::disk('public')->delete($question->image);
                            }
                            // Store the new image
                            $imagePath = $questionData['image']->store('questions', 'public');
                        } else {
                            $imagePath = $question->image;
                        }

                        // Update the question
                        $question->update([
                            'question_ar' => $questionData['question_ar'],
                            'question_en' => $questionData['question_en'],
                            'image' => $imagePath,
                        ]);
                    } else {
                        // Create a new question
                        $question = StudyNoteQuestion::create([
                            'study_note_exam_id' => $studyNoteExam->id,
                            'question_ar' => $questionData['question_ar'],
                            'question_en' => $questionData['question_en'],
                            'image' => $imagePath,
                        ]);
                    }

                    $questionIds[] = $question->id;
                    $answerIds = [];

                    // Check if answers exist for the question
                    if (isset($questionData['answers']) && is_array($questionData['answers'])) {
                        foreach ($questionData['answers'] as $answerIndex => $answerData) {
                            // Skip empty answers
                            if (empty($answerData['answer_ar']) && empty($answerData['answer_en'])) {
                                continue;
                            }

                            // Determine if this answer is correct
                            $isCorrect = isset($questionData['correct_answer']) && $answerIndex == $questionData['correct_answer'];

                            if (isset($answerData['id'])) {
                                $answer = StudyNoteAnswer::findOrFail($answerData['id']);
                                $answer->update([
                                    'answer_ar' => $answerData['answer_ar'],
                                    'answer_en' => $answerData['answer_en'],
                                    'is_correct' => $isCorrect,
                                ]);
                            } else {
                                $answer = StudyNoteAnswer::create([
                                    'study_note_question_id' => $question->id,
                                    'answer_ar' => $answerData['answer_ar'],
                                    'answer_en' => $answerData['answer_en'],
                                    'is_correct' => $isCorrect,
                                ]);
                            }

                            $answerIds[] = $answer->id;
                        }
                    }

                    // Delete answers that were removed in the update
                    $question->answers()->whereNotIn('id', $answerIds)->delete();
                }

                // Delete questions that were removed in the update
                $studyNoteExam->questions()->whereNotIn('id', $questionIds)->delete();
            }

            // Commit the transaction
            DB::commit();

            return redirect()->route('admin.study_note_exams.index')->with('success', transWord('تم التحديث بنجاح'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating exam:', ['error' => $e->getMessage()]);

            return redirect()->back()
                ->withErrors(['error' => transWord('حدث خطأ أثناء تحديث الاختبار. الرجاء المحاولة لاحقًا')])
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StudyNoteExam $studyNoteExam)
    {
        $studyNoteExam->delete();

        return response()->json(['message' => transWord('تم الحذف بنجاح')]);
    }
}
