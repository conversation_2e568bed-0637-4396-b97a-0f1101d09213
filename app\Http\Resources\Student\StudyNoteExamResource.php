<?php

namespace App\Http\Resources\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudyNoteExamResource extends JsonResource
{
    /**
     * Transform the resource into an array. *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $questions = $this?->questions()->orderBy('id', 'asc')->get();

        return [
            'id' => $this?->id,
            'name' => $this?->name,
            'duration' => $this?->duration,
            'questions' => QuestionResource::collection($questions),
        ];
    }
}
