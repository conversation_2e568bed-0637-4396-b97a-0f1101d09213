<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Student;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class StudentController extends Controller
{
    public function index(): View
    {
        $students = Student::all();

        return view('dashboard.students.index', compact('students'));
    }

    public function show(Student $student)
    {
        //
    }

    public function destroy(Student $student): JsonResponse
    {
        $student->delete();

        return response()->json([
            'message' => 'تم الحذف بنجاح',
        ]);
    }

    public function activeAccount(Student $student): JsonResponse
    {
        $student->update([
            'verified' => ! $student->verified,
        ]);

        if ($student->isAccountVerified) {
            return response()->json([
                'message' => transWord('تم تفعيل الحساب'),
                'verified' => true,
                'textValue' => transWord('مفعل'),
            ]);
        } else {
            return response()->json([
                'message' => transWord('تم إلغاء تفعيل الحساب'),
                'verified' => false,
                'textValue' => transWord('غير مفعل'),
            ]);
        }
    }
}
