<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\PlanRequest;
use App\Models\Plan;

class PlanController extends Controller
{
    public function index()
    {
        $plans = Plan::all();

        return view('dashboard.plans.index', compact('plans'));
    }

    public function create()
    {
        return view('dashboard.plans.create');
    }

    public function store(PlanRequest $request)
    {
        $data = $this->getData($request);

        Plan::create($data);

        return redirect()->route('admin.plans.index')->with('success', transWord('تمت الإضافة بنجاح'));
    }

    public function edit(Plan $plan)
    {
        return view('dashboard.plans.edit', compact('plan'));
    }

    public function update(PlanRequest $request, Plan $plan)
    {
        $data = $this->getData($request);

        // Update the plan with the new data
        $plan->update($data);

        return redirect()->route('admin.plans.index')->with('success', transWord('تمت التعديل بنجاح'));
    }

    public function destroy(Plan $plan)
    {
        // Check if the plan has any subscriptions
        if ($plan->subscriptions()->count() > 0) {
            return response()->json([
                'message' => transWord('لا يمكن حذف الخطة لأنها تحتوي على اشتراكات'),
            ], 400); // Return a 400 Bad Request response if there are subscriptions
        }

        $plan->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }

    protected function getData(PlanRequest $request): mixed
    {
        $data = $request->validated();

        // Restructure the features array into the desired format
        if (isset($data['features']) && is_array($data['features'])) {
            $structuredFeatures = ['ar' => [], 'en' => []];

            foreach ($data['features'] as $feature) {
                if (isset($feature['ar'])) {
                    $structuredFeatures['ar'][] = $feature['ar'];
                }
                if (isset($feature['en'])) {
                    $structuredFeatures['en'][] = $feature['en'];
                }
            }

            $data['features'] = json_encode($structuredFeatures);
        }

        return $data;
    }
}
