<?php

namespace App\Http\Resources\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExamPointResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'score' => $this->total_points,
            'student' => [
                'name' => $this->student->name,
                'image' => $this->student->image ? asset('storage/'.$this->student->image) : asset('app/images/user.png'),
            ],
        ];
    }
}
