<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        if ($request->expectsJson()) {
            return null;
        }

        // Check if the request is for an admin route
        if ($request->is('admin/*')) {
            return route('admin.login'); // Ensure this route exists
        }

        // Check if the request matches any teacher route (without prefix)
        if ($request->route() && $request->route()->getName() && str_starts_with($request->route()->getName(), 'teacher.')) {
            return route('teacher.login'); // Ensure this route exists
        }

        // Default redirect (optional)
        return route('teacher.login');
    }
}
