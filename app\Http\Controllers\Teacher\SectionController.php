<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Requests\Teacher\SectionRequest;

class SectionController extends Controller
{
    public function create(string $courseId)
    {
        $user = auth()->guard('teacher')->user();

        $course = $user->courses()->findOrFail($courseId);

        return view('teacher.sections.create', compact('course'));
    }

    public function store(SectionRequest $request, string $courseId)
    {
        $user = auth()->guard('teacher')->user();

        $course = $user->courses()->findOrFail($courseId);

        $data = $request->validated();

        $course->sections()->create($data);

        return redirect()->route('teacher.courses.show', $courseId)->with('success', transWord('تمت الإضافة بنجاح'));
    }

    public function edit(string $courseId, string $sectionId)
    {
        $user = auth()->guard('teacher')->user();

        $course = $user->courses()->findOrFail($courseId);

        $section = $course->sections()->findOrFail($sectionId);

        return view('teacher.sections.edit', compact('section', 'course'));
    }

    public function update(SectionRequest $request, string $courseId, string $sectionId)
    {
        $user = auth()->guard('teacher')->user();

        $course = $user->courses()->findOrFail($courseId);

        $section = $course->sections()->findOrFail($sectionId);

        $data = $request->validated();

        $section->update($data);

        return redirect()->route('teacher.courses.show', $courseId)->with('success', transWord('تم التحديث بنجاح'));
    }

    public function destroy(string $courseId, string $sectionId)
    {
        $user = auth()->guard('teacher')->user();

        $section = $user->courses()->findOrFail($courseId)->sections()->findOrFail($sectionId);

        $section->delete();

        return response()->json([
            'message' => transWord('تم الحذف بنجاح'),
        ]);
    }
}
