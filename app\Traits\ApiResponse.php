<?php

namespace App\Traits;

use Illuminate\Http\Response;

trait ApiResponse
{
    protected function response($data = [], $message = '', $status = 200): Response
    {
        $success = $this->isSuccessCode($status);

        return response([
            'data' => $data,
            'message' => $message,
            'success' => $success,
        ], $status);
    }

    protected function isSuccessCode($status): bool
    {
        return in_array($status, $this->successCodes());
    }

    protected function successCodes(): array
    {
        return [
            200, 201, 202,
        ];
    }
}
