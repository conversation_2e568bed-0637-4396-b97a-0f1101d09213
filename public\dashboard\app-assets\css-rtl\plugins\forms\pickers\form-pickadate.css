.picker__holder {
  outline : none;
}

.picker__day--today:before, .picker__button--today:before {
  border-top : 6px solid #7367F0;
}

.picker__button--clear:before {
  border-top : 2px solid #EA5455;
}

.picker__day--highlighted, .picker__day--highlighted:hover, .picker--focused .picker__day--highlighted, .picker__list-item--selected, .picker__list-item--selected:hover, .picker--focused .picker__list-item--selected {
  background-color : #7367F0;
}

.picker__nav--prev:before, .picker__nav--next:before, .picker__button--close:before {
  content : '';
  background-repeat : no-repeat;
  background-position : center;
  background-size : 18px;
  color : #6E6B7B;
  width : 8px;
  height : 18px;
}

.picker__nav--prev:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236e6b7b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-left\'%3E%3Cpolyline points=\'15 18 9 12 15 6\'%3E%3C/polyline%3E%3C/svg%3E');
}

.picker__nav--next:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236e6b7b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E');
}

.picker__button--close:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ea5455\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-x\'%3E%3Cline x1=\'18\' y1=\'6\' x2=\'6\' y2=\'18\'%3E%3C/line%3E%3Cline x1=\'6\' y1=\'6\' x2=\'18\' y2=\'18\'%3E%3C/line%3E%3C/svg%3E');
  height : 10px !important;
}

.dark-layout .picker__holder {
  background-color : #161D31;
  border-color : #3B4253;
}

.dark-layout .picker__holder .picker__header .picker__month, .dark-layout .picker__holder .picker__header .picker__year {
  color : #B4B7BD;
}

.dark-layout .picker__holder .picker__header .picker__select--year, .dark-layout .picker__holder .picker__header .picker__select--month {
  color : #B4B7BD;
  background-color : #161D31;
  border-color : #3B4253;
}

.dark-layout .picker__holder .picker__header .picker__nav--next:hover, .dark-layout .picker__holder .picker__header .picker__nav--prev:hover {
  background-color : #283046;
}

.dark-layout .picker__holder .picker__frame {
  border-color : #3B4253;
}

.dark-layout .picker__holder .picker__table thead tr .picker__weekday {
  color : #B4B7BD;
}

.dark-layout .picker__holder .picker__table tbody tr td .picker__day {
  color : #B4B7BD;
}

.dark-layout .picker__holder .picker__table tbody tr td .picker__day.picker__day--selected {
  color : #FFFFFF;
}

.dark-layout .picker__holder .picker__table tbody tr td .picker__day:hover {
  background-color : #3B4253;
  color : #FFFFFF;
}

.dark-layout .picker__holder .picker__table tbody tr td .picker__day.picker__day--disabled {
  color : #B4B7BD;
  opacity : 0.5;
  background : #283046;
}

.dark-layout .picker__holder .picker__table tbody tr td .picker__day--today {
  background-color : #283046;
}

.dark-layout .picker__holder .picker__footer .picker__button--today, .dark-layout .picker__holder .picker__footer .picker__button--clear, .dark-layout .picker__holder .picker__footer .picker__button--close {
  background-color : #161D31;
  color : #B4B7BD;
}

.dark-layout .picker__holder .picker__footer .picker__button--today:hover, .dark-layout .picker__holder .picker__footer .picker__button--clear:hover, .dark-layout .picker__holder .picker__footer .picker__button--close:hover {
  background-color : #3B4253;
}

.dark-layout .picker--time .picker__holder .picker__list {
  background-color : #161D31;
}

.dark-layout .picker--time .picker__holder .picker__list .picker__list-item.picker__list-item--selected, .dark-layout .picker--time .picker__holder .picker__list .picker__list-item:hover {
  background-color : #283046;
}

.dark-layout .picker--time .picker__holder .picker__list .picker__list-item.picker__list-item--disabled {
  background-color : #283046;
  color : #82868B;
  opacity : 0.5;
}

.dark-layout .picker--time .picker__holder .picker__list .picker__button--clear {
  background-color : #161D31;
  color : #B4B7BD;
}

[dir='rtl'] .picker__nav--prev:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236e6b7b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E');
}

[dir='rtl'] .picker__nav--next:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236e6b7b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-left\'%3E%3Cpolyline points=\'15 18 9 12 15 6\'%3E%3C/polyline%3E%3C/svg%3E');
}