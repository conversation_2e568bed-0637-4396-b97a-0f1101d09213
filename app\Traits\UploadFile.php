<?php

declare(strict_types=1);

namespace App\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

trait UploadFile
{
    public function uploadFile(UploadedFile $file, string $folder, $disk = 'public'): string
    {
        $originalName = str_replace([".{$file->extension()}", ' '], '', $file->getClientOriginalName());

        $fileName = time().'-'.$originalName.'.'.$file->extension();

        return $file->storeAs($folder, $fileName, $disk);
    }

    public function deleteFile($path, $disk = 'public')
    {
        Storage::disk($disk)->delete($path);
    }
}
