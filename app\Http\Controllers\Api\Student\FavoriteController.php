<?php

namespace App\Http\Controllers\Api\Student;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Student\FavoriteRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Student\FavoriteResource;

class FavoriteController extends Controller
{
    public function index()
    {
        $student = auth()->user();

        $favorites = $student->favorites()->with('course')->paginate(10);

        return $this->response(new BaseCollection($favorites, FavoriteResource::class));
    }

    public function store(FavoriteRequest $request)
    {
        $student = auth()->user();

        $courseId = $request->course_id;

        // Check if the course is already in the user's favorites
        $favorite = $student->favorites()->where('course_id', $courseId)->first();

        if ($favorite) {
            // If it exists, remove it
            $favorite->delete();

            return $this->response([], transWord('تم حذف الكورس من المفضلة'));
        } else {
            // If it doesn't exist, add it
            $student->favorites()->create(['course_id' => $courseId]);

            return $this->response([], transWord('تم إضافة الكورس إلى المفضلة'));
        }
    }
}
