<?php

namespace App\Http\Controllers\Api\Student;

use App\Models\Book;
use App\Models\Coupon;
use App\Models\Payment;
use App\Models\StudyNote;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Resources\Student\CartResource;
use App\Http\Requests\Api\Student\AddToCartRequest;
use App\Http\Requests\Api\Student\ApplyDiscountRequest;
use App\Http\Requests\Api\Student\RemoveFromCartRequest;

class CartController extends Controller
{
    public function cart()
    {
        $student = auth()->user();

        $cart = $student->cart;

        if (! $cart) {
            return $this->response([], transWord('لا يوجد عناصر في السله'));
        }

        return $this->response(new CartResource($cart));
    }

    public function addToCart(AddToCartRequest $request)
    {
        $student = auth()->user();

        DB::beginTransaction();
        try {
            // Retrieve the user's existing cart or create a new one
            $cart = $student->cart()->firstOrCreate([]);

            // Determine the correct model and table name
            $modelClass = $request->type === 'books' ? Book::class : StudyNote::class;
            $tableName = $request->type === 'books' ? 'books' : 'study_notes';

            // Find the item by ID
            $item = $modelClass::findOrFail($request->item_id);

            // Check if the item is already owned
            $alreadyOwned = $student->{$request->type}()->where("{$tableName}.id", $request->item_id)->exists();
            if ($alreadyOwned) {
                return $this->response(null, transWord('هذا العنصر لديك بالفعل'), 400);
            }

            // Check if the item is already in the cart
            $existingItem = $cart->items()
                ->where('itemable_id', $request->item_id)
                ->where('itemable_type', $modelClass)
                ->exists();

            if ($existingItem) {
                return $this->response(null, transWord('هذا العنصر موجود بالفعل في السلة'), 400);
            }

            // Add the new item to the cart
            $cart->items()->create([
                'itemable_id' => $item->id,
                'itemable_type' => $modelClass,
                'price' => $item->price,
            ]);

            // Update the cart total
            $cart->update([
                'total' => $cart->items()->sum('price'),
                'total_after_discount' => $cart->items()->sum('price'),
                'discount' => 0,
            ]);

            DB::commit();

            return $this->response([], transWord('تم الإضافة بنجاح'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return $this->response(null, transWord('حدث خطأ ما، الرجاء المحاولة مرة أخرى'), 400);
        }
    }

    public function removeFromCart(RemoveFromCartRequest $request)
    {
        $student = auth()->user();

        DB::beginTransaction();
        try {
            $cart = $student->cart;

            // Ensure the cart exists
            if (! $cart) {
                return $this->response(null, transWord('السلة غير موجودة'), 400);
            }

            // Retrieve the item from the cart's items
            $item = $cart->items()->where('id', $request->item_id)->first();

            if (! $item) {
                return $this->response(null, transWord('العنصر غير موجود'), 400);
            }

            // Delete the item
            $item->delete();

            // Update the cart total
            $cart->update([
                'total' => $cart->items()->sum('price'),
                'total_after_discount' => $cart->items()->sum('price'),
                'discount' => 0,
            ]);

            DB::commit();

            return $this->response([], transWord('تم الحذف بنجاح'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return $this->response(null, transWord('حدث خطأ ما، الرجاء المحاولة مرة أخرى'), 400);
        }
    }

    public function applyDiscount(ApplyDiscountRequest $request)
    {
        $coupon = Coupon::where('code', $request->discount_code)->first();

        if (! $coupon) {
            return $this->response(null, transWord('كود الخصم غير صحيح'), 400);
        }

        if ($coupon->expire_at < now()) {
            return $this->response(null, transWord('تم انتهاء صلاحية الكوبون'), 400);
        }

        $student = auth()->user();

        DB::beginTransaction();
        try {
            $cart = $student->cart;

            // Ensure the cart exists
            if (! $cart) {
                return $this->response(null, transWord('السلة غير موجودة'), 400);
            }

            if ($cart->discount > 0) {
                return $this->response(null, transWord('لقد تم تطبيق خصم من قبل'), 400);
            }

            $cart->update([
                'discount' => $coupon->discount,
                'total_after_discount' => $cart->total - ($cart->total * $coupon->discount / 100),
            ]);

            DB::commit();

            return $this->response([], transWord('تم تطبيق الخصم بنجاح'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return $this->response(null, transWord('حدث خطأ ما، الرجاء المحاولة مرة أخرى'), 400);
        }
    }

    public function purchase()
    {
        $student = auth()->user();

        DB::beginTransaction();
        try {
            $cart = $student->cart;

            // Ensure the cart exists
            if (! $cart) {
                return $this->response(null, transWord('السلة غير موجودة'), 400);
            }

            // Ensure the cart has items
            if ($cart->items->isEmpty()) {
                return $this->response(null, transWord('السلة فارغة'), 400);
            }

            // If cart total_after_discount is 0 (free items or 100% discount), process immediately
            if ($cart->total_after_discount == 0) {
                // Attach items to the student based on their type
                foreach ($cart->items as $item) {
                    $modelClass = $item->itemable_type;
                    $relation = $modelClass === \App\Models\Book::class ? 'books' : 'notes';

                    $student->{$relation}()->attach($item->itemable_id, [
                        'start_date' => now(),
                        'end_date' => now()->addMonth(),
                    ]);
                }

                // Clear the cart
                $cart->items()->delete();
                $cart->delete();

                DB::commit();
                return $this->response([], transWord('تم الشراء بنجاح'));
            }

            // For paid items, create a payment record
            $payment = Payment::create([
                'student_id' => $student->id,
                'type' => 'cart',
                'related_id' => $cart->id,
                'amount' => $cart->total_after_discount,
                'status' => 'pending',
            ]);

            // Generate payment URL
            $paymentUrl = url('/pay/') . '/' . $payment->id;

            DB::commit();

            return $this->response([
                'payment_url' => $paymentUrl,
            ], transWord('تم إنشاء طلب الدفع بنجاح'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return $this->response(null, transWord('حدث خطأ ما، الرجاء المحاولة مرة أخرى'), 400);
        }
    }
}



